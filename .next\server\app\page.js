/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBZ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvPzRmOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxQcm9qZWN0c1xcXFxOZXcgZm9sZGVyICgzKVxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2FwcC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDTmV3JTIwZm9sZGVyJTIwKDMpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDTmV3JTIwZm9sZGVyJTIwKDMpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDTmV3JTIwZm9sZGVyJTIwKDMpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJHJTNBJTVDJTVDUHJvamVjdHMlNUMlNUNOZXclMjBmb2xkZXIlMjAoMyklNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNub3QtZm91bmQtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDTmV3JTIwZm9sZGVyJTIwKDMpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQThIO0FBQzlIO0FBQ0Esb09BQStIO0FBQy9IO0FBQ0EsME9BQWtJO0FBQ2xJO0FBQ0Esd09BQWlJO0FBQ2pJO0FBQ0Esa1BBQXNJO0FBQ3RJO0FBQ0Esc1FBQWdKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLz82YjM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRzpcXFxcUHJvamVjdHNcXFxcTmV3IGZvbGRlciAoMylcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxhcHAtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxQcm9qZWN0c1xcXFxOZXcgZm9sZGVyICgzKVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxQcm9qZWN0c1xcXFxOZXcgZm9sZGVyICgzKVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxQcm9qZWN0c1xcXFxOZXcgZm9sZGVyICgzKVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkc6XFxcXFByb2plY3RzXFxcXE5ldyBmb2xkZXIgKDMpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxQcm9qZWN0c1xcXFxOZXcgZm9sZGVyICgzKVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/cohort-info-modal */ \"(ssr)/./components/ui/cohort-info-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction JoinCohortSplitButton() {\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [resetForm, setResetForm] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const handleSplit = ()=>{\n        setIsModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedOption(null);\n        setResetForm(true);\n        resetTheForm();\n    };\n    const resetTheForm = ()=>{\n        setResetForm(false);\n    };\n    const buttons = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: \"Project\",\n            action: \"Project\",\n            tooltip: \"Join as a project - Get feedback, ecosystem intros, and milestone rewards\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: \"Curator\",\n            action: \"Curator\",\n            tooltip: \"Join as a curator - Get project intros, monetization opportunities, and rewards\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex items-center justify-center h-20 mx-auto\",\n        children: [\n            \" \",\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__.CohortInfoModal, {\n                isOpen: isModalOpen,\n                onCloseAction: handleCloseModal\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: handleSplit,\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 text-lg md:px-12 md:py-5 md:text-xl font-bold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-opacity duration-300 max-w-xs sm:max-w-sm\",\n                children: [\n                    \" \",\n                    \"Join The First Cohort\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-5 h-5 ml-2 md:w-6 md:h-6 md:ml-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\nfunction HomePage() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    // Subtle scroll physics tracking\n    const scrollPhysicsRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)({\n        velocity: 0,\n        lastY: 0,\n        lastTime: 0,\n        effect: 0\n    });\n    // Fixed optimal animation settings\n    const animationSettings = {\n        numMetaballs: 21,\n        edgeWidth: 0.02,\n        speed: 2.1,\n        threshold: 1.14,\n        intensity: 12.5,\n        blueColor: {\n            r: 0.23,\n            g: 0.51,\n            b: 0.96\n        },\n        whiteColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        },\n        backgroundColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        }\n    };\n    // Handle scroll with subtle physics\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const currentTime = performance.now();\n            const currentY = window.scrollY;\n            const physics = scrollPhysicsRef.current;\n            // Calculate velocity only if we have previous data\n            if (physics.lastTime > 0) {\n                const deltaTime = currentTime - physics.lastTime;\n                const deltaY = currentY - physics.lastY;\n                if (deltaTime > 0) {\n                    // Smooth velocity calculation\n                    const newVelocity = deltaY / deltaTime;\n                    physics.velocity = physics.velocity * 0.7 + newVelocity * 0.3 // Smooth averaging\n                    ;\n                    // Create subtle effect based on velocity\n                    const maxEffect = 0.2 // Very subtle maximum effect\n                    ;\n                    physics.effect = Math.max(-maxEffect, Math.min(maxEffect, physics.velocity * 0.1));\n                }\n            }\n            physics.lastTime = currentTime;\n            physics.lastY = currentY;\n            setScrollY(currentY);\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Gentle decay of scroll effects\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const decayInterval = setInterval(()=>{\n            const physics = scrollPhysicsRef.current;\n            physics.effect *= 0.88 // Very gentle decay\n            ;\n            physics.velocity *= 0.95;\n        }, 16);\n        return ()=>clearInterval(decayInterval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const dpr = window.devicePixelRatio || 1;\n        const cssWidth = window.innerWidth;\n        const cssHeight = window.innerHeight;\n        // Use device-pixel–scaled dimensions for all WebGL maths so the effect fills the entire screen on high-DPR (e.g. mobile) displays\n        const width = cssWidth * dpr;\n        const height = cssHeight * dpr;\n        canvas.width = width;\n        canvas.height = height;\n        const gl = canvas.getContext(\"webgl\");\n        if (!gl) {\n            console.error(\"WebGL not supported\");\n            return;\n        }\n        gl.viewport(0, 0, canvas.width, canvas.height); // Set viewport for DPR\n        console.log(\"WebGL context created successfully\");\n        const mouse = {\n            x: 0,\n            y: 0\n        };\n        const numMetaballs = animationSettings.numMetaballs;\n        const metaballs = [];\n        // Calculate responsive radii based on the smaller dimension, making them larger\n        const baseDimension = Math.min(width, height);\n        const responsiveMinRadius = baseDimension * 0.1; // Doubled from 0.05\n        const responsiveRadiusRange = baseDimension * 0.08; // Doubled from 0.04\n        // Initialize metaballs with configurable settings\n        for(let i = 0; i < numMetaballs; i++){\n            const radius = Math.random() * responsiveRadiusRange + responsiveMinRadius;\n            metaballs.push({\n                x: Math.random() * (width - 2 * radius) + radius,\n                y: Math.random() * (height - 2 * radius) + radius,\n                vx: (Math.random() - 0.5) * animationSettings.speed,\n                vy: (Math.random() - 0.5) * animationSettings.speed,\n                r: radius * 0.75\n            });\n        }\n        const vertexShaderSrc = `\r\n      attribute vec2 position;\r\n      void main() {\r\n        gl_Position = vec4(position, 0.0, 1.0);\r\n      }\r\n    `;\n        const fragmentShaderSrc = `\r\n      precision highp float;\r\n      uniform float u_width;\r\n      uniform float u_height;\r\n      uniform int u_numMetaballs;\r\n      uniform vec3 metaballs[21];\r\n      uniform vec3 u_blueColor;\r\n      uniform vec3 u_whiteColor;\r\n      uniform vec3 u_backgroundColor;\r\n\r\n      void main(){\r\n        float x = gl_FragCoord.x;\r\n        float y = gl_FragCoord.y;\r\n        float sum = 0.0;\r\n\r\n        for (int i = 0; i < 21; i++) {\r\n          if (i >= u_numMetaballs) break;\r\n          vec3 metaball = metaballs[i];\r\n          float dx = metaball.x - x;\r\n          float dy = metaball.y - y;\r\n          float radius = metaball.z;\r\n          sum += (radius * radius) / (dx * dx + dy * dy);\r\n        }\r\n\r\n        float threshold = 0.5;\r\n        float edgeWidth = 0.02;\r\n\r\n        if (sum >= threshold - edgeWidth) {\r\n          float intensity = min(1.0, (sum - threshold) * 20.0);\r\n          vec3 color = mix(u_blueColor, u_whiteColor, intensity);\r\n          float alpha = smoothstep(threshold - edgeWidth, threshold + edgeWidth, sum);\r\n          gl_FragColor = vec4(color, alpha);\r\n          return;\r\n        }\r\n\r\n        gl_FragColor = vec4(u_backgroundColor, 1.0);\r\n      }\r\n    `;\n        // Helper functions for WebGL\n        const compileShader = (gl, shaderSource, shaderType)=>{\n            const shader = gl.createShader(shaderType);\n            if (!shader) throw new Error(\"Could not create shader\");\n            gl.shaderSource(shader, shaderSource);\n            gl.compileShader(shader);\n            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {\n                throw new Error(\"Shader compile failed with: \" + gl.getShaderInfoLog(shader));\n            }\n            return shader;\n        };\n        const getUniformLocation = (gl, program, name)=>{\n            const uniformLocation = gl.getUniformLocation(program, name);\n            if (uniformLocation === -1) {\n                throw new Error(\"Can not find uniform \" + name);\n            }\n            return uniformLocation;\n        };\n        const getAttribLocation = (gl, program, name)=>{\n            const attributeLocation = gl.getAttribLocation(program, name);\n            if (attributeLocation === -1) {\n                throw new Error(\"Can not find attribute \" + name);\n            }\n            return attributeLocation;\n        };\n        const vertexShader = compileShader(gl, vertexShaderSrc, gl.VERTEX_SHADER);\n        const fragmentShader = compileShader(gl, fragmentShaderSrc, gl.FRAGMENT_SHADER);\n        const program = gl.createProgram();\n        if (!program) throw new Error(\"Could not create program\");\n        gl.attachShader(program, vertexShader);\n        gl.attachShader(program, fragmentShader);\n        gl.linkProgram(program);\n        const vertexData = new Float32Array([\n            -1.0,\n            1.0,\n            -1.0,\n            -1.0,\n            1.0,\n            1.0,\n            1.0,\n            -1.0\n        ]);\n        const vertexDataBuffer = gl.createBuffer();\n        gl.bindBuffer(gl.ARRAY_BUFFER, vertexDataBuffer);\n        gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW);\n        const positionHandle = getAttribLocation(gl, program, \"position\");\n        gl.enableVertexAttribArray(positionHandle);\n        gl.vertexAttribPointer(positionHandle, 2, gl.FLOAT, false, 2 * 4, 0);\n        const metaballsHandle = getUniformLocation(gl, program, \"metaballs\");\n        const numMetaballsHandle = getUniformLocation(gl, program, \"u_numMetaballs\");\n        const widthHandle = getUniformLocation(gl, program, \"u_width\");\n        const heightHandle = getUniformLocation(gl, program, \"u_height\");\n        const blueColorHandle = getUniformLocation(gl, program, \"u_blueColor\");\n        const whiteColorHandle = getUniformLocation(gl, program, \"u_whiteColor\");\n        const backgroundColorHandle = getUniformLocation(gl, program, \"u_backgroundColor\");\n        const loop = ()=>{\n            // Get subtle scroll physics effect\n            const scrollEffect = scrollPhysicsRef.current.effect;\n            const time = Date.now() * 0.001 // Convert to seconds\n            ;\n            // Update metaballs with continuous movement and scroll-responsive effects\n            for(let i = 0; i < numMetaballs; i++){\n                const metaball = metaballs[i];\n                // Base continuous movement - always active (slowed down)\n                const baseSpeed = animationSettings.speed * 0.077 // 15% of original speed as base (half of previous)\n                ;\n                const staticWiggleX = Math.sin(time * 0.25 + i * 1.2) * baseSpeed * 0.4 // Half frequency\n                ;\n                const staticWiggleY = Math.cos(time * 0.15 + i * 0.8) * baseSpeed * 0.3 // Half frequency\n                ;\n                // Apply base movement with static wiggle (reduced intensity)\n                let newVx = metaball.vx + staticWiggleX * 0.06;\n                let newVy = metaball.vy + staticWiggleY * 0.05;\n                // Add scroll physics on top of base movement\n                if (Math.abs(scrollEffect) > 0.01) {\n                    // Subtle vertical drift based on scroll direction\n                    newVy += scrollEffect * 0.25;\n                    // Enhanced horizontal wiggle during scroll (slowed down)\n                    const scrollWiggle = Math.sin(time * 1 + i * 0.7) * Math.abs(scrollEffect) * 0.1 // Half frequency\n                    ;\n                    newVx += scrollWiggle;\n                    // Tiny bit of randomness for natural variation\n                    newVx += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.05;\n                    newVy += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.03;\n                }\n                // Ensure minimum movement to prevent stagnation\n                const minSpeed = 0.2;\n                if (Math.abs(newVx) < minSpeed) newVx += (Math.random() - 0.5) * minSpeed;\n                if (Math.abs(newVy) < minSpeed) newVy += (Math.random() - 0.5) * minSpeed;\n                // Update position with modified velocities\n                metaball.x += newVx;\n                metaball.y += newVy;\n                // Store back the velocities with less aggressive damping\n                metaball.vx = newVx * 0.995 // Less damping to maintain movement\n                ;\n                metaball.vy = newVy * 0.995;\n                // Boundary collision\n                if (metaball.x < metaball.r || metaball.x > width - metaball.r) metaball.vx *= -1;\n                if (metaball.y < metaball.r || metaball.y > height - metaball.r) metaball.vy *= -1;\n            }\n            const dataToSendToGPU = new Float32Array(3 * numMetaballs);\n            for(let i = 0; i < numMetaballs; i++){\n                const baseIndex = 3 * i;\n                const mb = metaballs[i];\n                dataToSendToGPU[baseIndex + 0] = mb.x;\n                dataToSendToGPU[baseIndex + 1] = mb.y;\n                dataToSendToGPU[baseIndex + 2] = mb.r;\n            }\n            gl.uniform3fv(metaballsHandle, dataToSendToGPU);\n            gl.uniform1i(numMetaballsHandle, numMetaballs); // Set numMetaballs uniform\n            gl.uniform1f(widthHandle, width);\n            gl.uniform1f(heightHandle, height);\n            gl.uniform3f(blueColorHandle, animationSettings.blueColor.r, animationSettings.blueColor.g, animationSettings.blueColor.b);\n            gl.uniform3f(whiteColorHandle, animationSettings.whiteColor.r, animationSettings.whiteColor.g, animationSettings.whiteColor.b);\n            gl.uniform3f(backgroundColorHandle, animationSettings.backgroundColor.r, animationSettings.backgroundColor.g, animationSettings.backgroundColor.b);\n            gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);\n            animationRef.current = requestAnimationFrame(loop);\n        };\n        // Mouse interaction\n        const handleMouseMove = (e)=>{\n            const rect = canvas.getBoundingClientRect();\n            mouse.x = e.clientX - rect.left;\n            mouse.y = e.clientY - rect.top;\n        };\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        // Handle resize\n        const handleResize = ()=>{\n            const newDpr = window.devicePixelRatio || 1;\n            canvas.width = window.innerWidth * newDpr;\n            canvas.height = window.innerHeight * newDpr;\n            gl.viewport(0, 0, canvas.width, canvas.height);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        gl.useProgram(program);\n        loop();\n        return ()=>{\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            canvas.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"min-h-screen bg-white relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    zIndex: 1,\n                    transform: `translateY(${scrollY * 0.3}px)` // Parallax effect\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"fixed inset-0 w-full h-full pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + `bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300 ${scrollY > 50 ? \"shadow-lg\" : \"shadow-sm\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-lg\",\n                                            children: \"F\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-900 font-bold text-xl tracking-tight\",\n                                        children: \"Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Projects\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Curators\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Community\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Docs\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Connect Wallet\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"relative py-20 lg:py-32 min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-6xl mx-auto px-6 text-center relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-display text-gray-900 mb-8 max-w-4xl mx-auto\",\n                            children: [\n                                \"Discover & Support\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        filter: \"drop-shadow(0 0 20px rgba(59, 130, 246, 0.3))\",\n                                        animation: \"web3Pulse 3s ease-in-out infinite\"\n                                    },\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"Web3 Startups\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 mb-12 max-w-4xl mx-auto\",\n                            children: \"Forcefi is a decentralized launchpad that bridges industry experts with Web3 Startups. List your early-stage project, or monetize your skills as a platform curator.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center gap-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JoinCohortSplitButton, {}, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-wrap justify-center items-center gap-4 mt-8 sm:gap-6\",\n                            children: [\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Follow on X\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Read Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5) 70%, rgba(255, 255, 255, 0) 100%)\",\n                    backdropFilter: \"blur(7px)\"\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-16 relative z-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-extrabold text-gray-900 mb-6\",\n                                        children: \"First Cohort Benefits\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: \"As part of Forcefi’s first cohort, you receive a number of benefits that help shape your product and build an audience for your project and services. Here is a breakdown of what you can expect:\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Biweekly feedback sessions on product, marketing, and BD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Contextually relevant product deepdive & advice\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Ecosystem intros and relevant partnership opportunities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Educational materials and discounts on external courses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent support in token grants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Curators\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Intros to projects that require your services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Monetization opportunities through platform participation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Small pool of competitors (only the best are accepted)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Increase your reach and grow your audience\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent token rewards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"section-spacing relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"Three Ways to Participate\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 max-w-2xl mx-auto\",\n                                            children: \"Join our ecosystem as a project, curator, or investor and shape the Web3 future\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Access our vetted network of 70+ Listed Projects to find talent and growth opportunities. Accelerate your project with community expertise.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Learn More →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Curators\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Support promising projects and earn income. Join our network of 200+ Expert Curators making a real impact in Web3.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-purple-600 hover:text-purple-700 hover:bg-purple-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Get Involved →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Investors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Invest in curated projects at the earliest stage. Earn through staking and support innovative Web3 projects. Access exclusive early-stage opportunities backed by our expert curators.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-green-600 hover:text-green-700 hover:bg-green-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Explore Now →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-20 relative z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute inset-0 bg-white/10 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10 mb-24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"shadow-2xl hover:shadow-3xl transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pt-12 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-8 h-8 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Something Big is Coming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                children: \"Stay tuned for Forcefi's token launch, empowering our ecosystem. Details soon!\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-10 h-10 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-2xl font-bold text-gray-900 mt-4\",\n                                                children: \"Join the Waitlist\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 mt-2\",\n                                                children: \"Be the first to know when we launch\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-6\",\n                                        children: \"Be the First to Experience Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Follow our socials and learn more about our platform to benefit as an early-stage user. If you have questions feel free to reach out and our team will get back to you.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-md mx-auto mb-12 px-4 sm:px-0\",\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col sm:flex-row gap-3\",\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"emailInput\",\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"bg-white border-gray-300 focus:border-blue-500 text-gray-900 placeholder:text-gray-500 text-base shadow-sm flex-grow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 whitespace-nowrap font-semibold shadow-lg w-full sm:w-auto\" // Made button full width on mobile\n                                                        ,\n                                                        onClick: ()=>{\n                                                            const emailInput = document.getElementById(\"emailInput\");\n                                                            const email = emailInput.value;\n                                                            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                                                            if (emailRegex.test(email)) {\n                                                                const button = emailInput.nextElementSibling;\n                                                                button.textContent = \"Subscribed\";\n                                                                button.classList.remove(\"bg-blue-600\", \"hover:bg-blue-700\");\n                                                                button.classList.add(\"bg-green-600\", \"hover:bg-green-700\");\n                                                            } else {\n                                                                alert(\"Please enter a valid email address.\");\n                                                            }\n                                                        },\n                                                        children: \"Join the Waitlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gray-900 py-16 relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-7xl mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center p-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/logo.svg\",\n                                                    alt: \"Forcefi Logo\",\n                                                    style: {\n                                                        filter: \"brightness(0) invert(1)\"\n                                                    },\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-2xl\",\n                                                children: \"Forcefi\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Follow on X\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Discord\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Read Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Telegram\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-400 text-sm\",\n                                        children: \"\\xa9 2025 Forcefi. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 691,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"dacc1f6cc99453e7\",\n                children: \"@-webkit-keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}@-moz-keyframes web3Pulse{0%,100%{-moz-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-moz-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@-o-keyframes web3Pulse{0%,100%{-o-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-o-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);-moz-transform:scale(1.02);-o-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}.holographic-card.jsx-dacc1f6cc99453e7{width:100%;background:#111;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;position:relative;overflow:hidden;-webkit-border-radius:15px;-moz-border-radius:15px;border-radius:15px;-webkit-transition:all.5s ease;-moz-transition:all.5s ease;-o-transition:all.5s ease;transition:all.5s ease;-webkit-box-shadow:0 10px 20px rgba(0,0,0,.2);-moz-box-shadow:0 10px 20px rgba(0,0,0,.2);box-shadow:0 10px 20px rgba(0,0,0,.2)}.holographic-card.jsx-dacc1f6cc99453e7:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);-moz-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 400,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Badge,badgeVariants auto */ \n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/cohort-info-modal.tsx":
/*!*********************************************!*\
  !*** ./components/ui/cohort-info-modal.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CohortInfoModal: () => (/* binding */ CohortInfoModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ CohortInfoModal auto */ \n\n\n\n\n\n\n\n\nconst expertiseOptions = [\n    \"Blockchain Development\",\n    \"Smart Contracts\",\n    \"DeFi Protocols\",\n    \"NFT Development\",\n    \"Web3 Marketing\",\n    \"Tokenomics\",\n    \"Community Building\",\n    \"Product Strategy\",\n    \"Fundraising\",\n    \"Legal & Compliance\",\n    \"UI/UX Design\",\n    \"Security Auditing\"\n];\nfunction CohortInfoModal({ isOpen, onCloseAction }) {\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [projectFormData, setProjectFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        website: \"\",\n        chain: \"\",\n        type: \"\",\n        description: \"\",\n        teamMembers: \"\",\n        email: \"\",\n        goals: \"\",\n        twitter: \"\",\n        discord: \"\",\n        telegram: \"\"\n    });\n    const [otherChain, setOtherChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [curatorFormData, setCuratorFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        services: \"\",\n        documents: \"\",\n        availability: \"\",\n        expertise: [],\n        email: \"\",\n        twitter: \"\",\n        linkedin: \"\",\n        discord: \"\"\n    });\n    const resetForm = ()=>{\n        setSelectedRole(null);\n        setCurrentStep(1);\n        setProjectFormData({\n            title: \"\",\n            website: \"\",\n            chain: \"\",\n            type: \"\",\n            description: \"\",\n            teamMembers: \"\",\n            email: \"\",\n            goals: \"\",\n            twitter: \"\",\n            discord: \"\",\n            telegram: \"\"\n        });\n        setCuratorFormData({\n            name: \"\",\n            services: \"\",\n            documents: \"\",\n            availability: \"\",\n            expertise: [],\n            email: \"\",\n            twitter: \"\",\n            linkedin: \"\",\n            discord: \"\"\n        });\n    };\n    const handleClose = ()=>{\n        resetForm();\n        onCloseAction(); // Updated reference\n    };\n    const handleRoleSelect = (role)=>{\n        setSelectedRole(role);\n        setCurrentStep(2);\n    };\n    const handleBack = ()=>{\n        if (currentStep === 2) {\n            setSelectedRole(null);\n            setCurrentStep(1);\n        } else {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleNext = ()=>{\n        setCurrentStep(currentStep + 1);\n    };\n    const handleSubmit = ()=>{\n        const formData = selectedRole === \"project\" ? projectFormData : curatorFormData;\n        console.log(`${selectedRole} application submitted:`, formData);\n        // Here you would typically send the data to your backend\n        setIsSubmitted(true);\n    };\n    const toggleExpertise = (skill)=>{\n        setCuratorFormData((prev)=>({\n                ...prev,\n                expertise: prev.expertise.includes(skill) ? prev.expertise.filter((s)=>s !== skill) : [\n                    ...prev.expertise,\n                    skill\n                ]\n            }));\n    };\n    const getProgressPercentage = ()=>{\n        if (!selectedRole) return 0;\n        return (currentStep - 1) / 3 * 100;\n    };\n    const renderRoleSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Choose your path to shape the future of Web3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-blue-500\",\n                            onClick: ()=>handleRoleSelect(\"project\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Project\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center pb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        className: \"text-gray-600\",\n                                        children: \"Apply with your Web3 project to get expert guidance and community support\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-green-500\",\n                            onClick: ()=>handleRoleSelect(\"curator\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-green-50 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Curator\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center pb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        className: \"text-gray-600\",\n                                        children: \"Share your expertise and earn by mentoring the next generation of Web3 startups\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 142,\n            columnNumber: 5\n        }, this);\n    const renderProjectForm = ()=>{\n        switch(currentStep){\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Basic Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Enter your project name\",\n                                            value: projectFormData.title,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Website URL\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"https://yourproject.com\",\n                                            value: projectFormData.website,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        website: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Blockchain *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: projectFormData.chain,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        chain: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select blockchain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ethereum\",\n                                                    children: \"Ethereum\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"polygon\",\n                                                    children: \"Polygon\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"bsc\",\n                                                    children: \"BSC\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"solana\",\n                                                    children: \"Solana\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        projectFormData.chain === \"other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Please specify\",\n                                            value: otherChain,\n                                            onChange: (e)=>{\n                                                setOtherChain(e.target.value);\n                                                setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    }));\n                                            },\n                                            className: \"mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Type *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: projectFormData.type,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select project type\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"defi\",\n                                                    children: \"DeFi\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"nft\",\n                                                    children: \"NFT\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"gaming\",\n                                                    children: \"Gaming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"infrastructure\",\n                                                    children: \"Infrastructure\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Team & Contact\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Description *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"Describe your project, its goals, and what makes it unique...\",\n                                            value: projectFormData.description,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Team Members\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"List key team members and their roles\",\n                                            value: projectFormData.teamMembers,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        teamMembers: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Contact Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"email\",\n                                            placeholder: \"<EMAIL>\",\n                                            value: projectFormData.email,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        email: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Goals & Social\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Goals *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"What do you hope to achieve through this cohort?\",\n                                            value: projectFormData.goals,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        goals: e.target.value\n                                                    })),\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Twitter\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"@yourproject\",\n                                            value: projectFormData.twitter,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        twitter: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Discord server invite\",\n                                            value: projectFormData.discord,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        discord: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Telegram group/channel\",\n                                            value: projectFormData.telegram,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        telegram: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const renderCuratorForm = ()=>{\n        switch(currentStep){\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Personal Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Full Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Enter your full name\",\n                                            value: curatorFormData.name,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Services Offered *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"Describe the services and expertise you can provide to Web3 projects...\",\n                                            value: curatorFormData.services,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        services: e.target.value\n                                                    })),\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Portfolio/Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Links to your portfolio, resume, or relevant documents\",\n                                            value: curatorFormData.documents,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        documents: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 11\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Availability & Expertise\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Availability *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: curatorFormData.availability,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        availability: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select your availability\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"full-time\",\n                                                    children: \"Full-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"part-time\",\n                                                    children: \"Part-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"weekends\",\n                                                    children: \"Weekends\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"flexible\",\n                                                    children: \"Flexible\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Expertise Areas *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: expertiseOptions.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: curatorFormData.expertise.includes(skill) ? \"default\" : \"outline\",\n                                                    className: `cursor-pointer transition-colors ${curatorFormData.expertise.includes(skill) ? \"bg-blue-600 hover:bg-blue-700\" : \"hover:bg-blue-50\"}`,\n                                                    onClick: ()=>toggleExpertise(skill),\n                                                    children: skill\n                                                }, skill, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 11\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Contact Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"email\",\n                                            placeholder: \"<EMAIL>\",\n                                            value: curatorFormData.email,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        email: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Twitter\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"@yourusername\",\n                                            value: curatorFormData.twitter,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        twitter: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"LinkedIn\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"LinkedIn profile URL\",\n                                            value: curatorFormData.linkedin,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        linkedin: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Discord username\",\n                                            value: curatorFormData.discord,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        discord: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const renderThankYouMessage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: \"Thank you for applying to Forcefi’s first cohort.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"We will review your submissions and be in touch.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 453,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n        open: isOpen,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                    className: \"relative\",\n                    children: !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                        className: \"text-center\",\n                        children: \"Join The First Cohort\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this),\n                isSubmitted ? renderThankYouMessage() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Step \",\n                                                currentStep - 1,\n                                                \" of 3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                Math.round(getProgressPercentage()),\n                                                \"% Complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: `${getProgressPercentage()}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                !selectedRole && renderRoleSelection(),\n                                selectedRole === \"project\" && renderProjectForm(),\n                                selectedRole === \"curator\" && renderCuratorForm()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBack,\n                                    className: \"flex items-center gap-2 bg-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, this),\n                                currentStep < 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleNext,\n                                    className: \"flex items-center gap-2 bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        \"Next\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSubmit,\n                                    className: \"flex items-center gap-2 bg-green-600 hover:bg-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Submit Application\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 461,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n        lineNumber: 460,\n        columnNumber: 5\n    }, this);\n}\n// Add CSS for the hover effect\nconst JoinWaitlistButton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#\",\n        className: \"rainbow-button\",\n        alt: \"Join the Waitlist\",\n        style: {\n            width: \"calc(20vw + 6px)\",\n            height: \"calc(8vw + 6px)\",\n            backgroundImage: \"linear-gradient(90deg, #00C0FF 0%, #FFCF00 49%, #FC4F4F 80%, #00C0FF 100%)\",\n            borderRadius: \"5px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            textTransform: \"uppercase\",\n            fontSize: \"3vw\",\n            fontWeight: \"bold\",\n            animation: \"slidebg 2s linear infinite\"\n        },\n        children: \"Join the Waitlist\"\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n        lineNumber: 524,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/cohort-info-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 87,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 102,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLG9XQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1ibHVlLTUwMCBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Textarea auto */ \n\n\n\n\nconst textareaVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-background\",\n            destructive: \"border-destructive bg-destructive text-destructive-foreground shadow-sm placeholder:text-muted-foreground hover:border-destructive/40\"\n        },\n        size: {\n            sm: \"h-9 rounded-md\",\n            lg: \"h-11 rounded-md px-4\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"sm\"\n    }\n});\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"textarea\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(textareaVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxtQkFBbUJGLDZEQUFHQSxDQUMxQiw0U0FDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1FBQ0o7UUFDQUMsTUFBTTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7UUFDTjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmTixTQUFTO1FBQ1RHLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUkseUJBQVdiLDZDQUFnQixDQUcvQixDQUFDLEVBQUVlLFNBQVMsRUFBRVQsT0FBTyxFQUFFRyxJQUFJLEVBQUVPLFVBQVUsS0FBSyxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDMUQsTUFBTUMsT0FBT0gsVUFBVWYsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUNrQjtRQUNDRCxLQUFLQTtRQUNMSCxXQUFXWiw4Q0FBRUEsQ0FBQ0MsaUJBQWlCO1lBQUVFO1lBQVNHO1lBQU1NO1FBQVU7UUFDekQsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFDQUosU0FBU08sV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeD9iODAyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCJcclxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgdGV4dGFyZWFWYXJpYW50cyA9IGN2YShcclxuICBcImZsZXggbWluLWgtWzYwcHhdIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctYmx1ZS01MDAgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXHJcbiAge1xyXG4gICAgdmFyaWFudHM6IHtcclxuICAgICAgdmFyaWFudDoge1xyXG4gICAgICAgIGRlZmF1bHQ6IFwiYmctYmFja2dyb3VuZFwiLFxyXG4gICAgICAgIGRlc3RydWN0aXZlOlxyXG4gICAgICAgICAgXCJib3JkZXItZGVzdHJ1Y3RpdmUgYmctZGVzdHJ1Y3RpdmUgdGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIHNoYWRvdy1zbSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6Ym9yZGVyLWRlc3RydWN0aXZlLzQwXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNpemU6IHtcclxuICAgICAgICBzbTogXCJoLTkgcm91bmRlZC1tZFwiLFxyXG4gICAgICAgIGxnOiBcImgtMTEgcm91bmRlZC1tZCBweC00XCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XHJcbiAgICAgIHZhcmlhbnQ6IFwiZGVmYXVsdFwiLFxyXG4gICAgICBzaXplOiBcInNtXCIsXHJcbiAgICB9LFxyXG4gIH1cclxuKVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBUZXh0YXJlYVByb3BzXHJcbiAgZXh0ZW5kcyBSZWFjdC5UZXh0YXJlYUhUTUxBdHRyaWJ1dGVzPEhUTUxUZXh0QXJlYUVsZW1lbnQ+LFxyXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiB0ZXh0YXJlYVZhcmlhbnRzPiB7XHJcbiAgYXNDaGlsZD86IGJvb2xlYW5cclxufVxyXG5cclxuY29uc3QgVGV4dGFyZWEgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIEhUTUxUZXh0QXJlYUVsZW1lbnQsXHJcbiAgVGV4dGFyZWFQcm9wc1xyXG4+KCh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcInRleHRhcmVhXCJcclxuICByZXR1cm4gKFxyXG4gICAgPENvbXBcclxuICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgIGNsYXNzTmFtZT17Y24odGV4dGFyZWFWYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XHJcbiAgICAgIHsuLi5wcm9wc31cclxuICAgIC8+XHJcbiAgKVxyXG59KVxyXG5UZXh0YXJlYS5kaXNwbGF5TmFtZSA9IFwiVGV4dGFyZWFcIlxyXG5cclxuZXhwb3J0IHsgVGV4dGFyZWEgfVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJ0ZXh0YXJlYVZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwic2l6ZSIsInNtIiwibGciLCJkZWZhdWx0VmFyaWFudHMiLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwicHJvcHMiLCJyZWYiLCJDb21wIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"18636565857b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvLi9hcHAvZ2xvYmFscy5jc3M/OTAyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE4NjM2NTY1ODU3YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Forcefi - Shape the Future of Web3\",\n    description: \"Forcefi is your decentralized launchpad, uniting startups with expert curators. Launch your vision or join our community today.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7WUFBRVEsd0JBQXdCO3NCQUFFSDs7Ozs7Ozs7Ozs7QUFHbEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRm9yY2VmaSAtIFNoYXBlIHRoZSBGdXR1cmUgb2YgV2ViMycsXG4gIGRlc2NyaXB0aW9uOiAnRm9yY2VmaSBpcyB5b3VyIGRlY2VudHJhbGl6ZWQgbGF1bmNocGFkLCB1bml0aW5nIHN0YXJ0dXBzIHdpdGggZXhwZXJ0IGN1cmF0b3JzLiBMYXVuY2ggeW91ciB2aXNpb24gb3Igam9pbiBvdXIgY29tbXVuaXR5IHRvZGF5LicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfSBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`G:\Projects\New folder (3)\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`G:\Projects\New folder (3)\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/react-remove-scroll","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/styled-jsx","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/get-nonce","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();