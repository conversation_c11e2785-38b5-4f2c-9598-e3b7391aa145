/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBZ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvPzRmOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxQcm9qZWN0c1xcXFxOZXcgZm9sZGVyICgzKVxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/cohort-info-modal */ \"(ssr)/./components/ui/cohort-info-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction JoinCohortSplitButton() {\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [resetForm, setResetForm] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const handleSplit = ()=>{\n        setIsModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedOption(null);\n        setResetForm(true);\n        resetTheForm();\n    };\n    const resetTheForm = ()=>{\n        setResetForm(false);\n    };\n    const buttons = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: \"Project\",\n            action: \"Project\",\n            tooltip: \"Join as a project - Get feedback, ecosystem intros, and milestone rewards\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: \"Curator\",\n            action: \"Curator\",\n            tooltip: \"Join as a curator - Get project intros, monetization opportunities, and rewards\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex items-center justify-center h-20 mx-auto\",\n        children: [\n            \" \",\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__.CohortInfoModal, {\n                isOpen: isModalOpen,\n                onCloseAction: handleCloseModal\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: handleSplit,\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 text-lg md:px-12 md:py-5 md:text-xl font-bold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-opacity duration-300 max-w-xs sm:max-w-sm\",\n                children: [\n                    \" \",\n                    \"Join The First Cohort\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-5 h-5 ml-2 md:w-6 md:h-6 md:ml-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\nfunction HomePage() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    // Subtle scroll physics tracking\n    const scrollPhysicsRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)({\n        velocity: 0,\n        lastY: 0,\n        lastTime: 0,\n        effect: 0\n    });\n    // Fixed optimal animation settings\n    const animationSettings = {\n        numMetaballs: 21,\n        edgeWidth: 0.02,\n        speed: 2.1,\n        threshold: 1.14,\n        intensity: 12.5,\n        blueColor: {\n            r: 0.23,\n            g: 0.51,\n            b: 0.96\n        },\n        whiteColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        },\n        backgroundColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        }\n    };\n    // Handle scroll with subtle physics\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const currentTime = performance.now();\n            const currentY = window.scrollY;\n            const physics = scrollPhysicsRef.current;\n            // Calculate velocity only if we have previous data\n            if (physics.lastTime > 0) {\n                const deltaTime = currentTime - physics.lastTime;\n                const deltaY = currentY - physics.lastY;\n                if (deltaTime > 0) {\n                    // Smooth velocity calculation\n                    const newVelocity = deltaY / deltaTime;\n                    physics.velocity = physics.velocity * 0.7 + newVelocity * 0.3 // Smooth averaging\n                    ;\n                    // Create subtle effect based on velocity\n                    const maxEffect = 0.2 // Very subtle maximum effect\n                    ;\n                    physics.effect = Math.max(-maxEffect, Math.min(maxEffect, physics.velocity * 0.1));\n                }\n            }\n            physics.lastTime = currentTime;\n            physics.lastY = currentY;\n            setScrollY(currentY);\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Gentle decay of scroll effects\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const decayInterval = setInterval(()=>{\n            const physics = scrollPhysicsRef.current;\n            physics.effect *= 0.88 // Very gentle decay\n            ;\n            physics.velocity *= 0.95;\n        }, 16);\n        return ()=>clearInterval(decayInterval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const dpr = window.devicePixelRatio || 1;\n        const cssWidth = window.innerWidth;\n        const cssHeight = window.innerHeight;\n        // Use device-pixel–scaled dimensions for all WebGL maths so the effect fills the entire screen on high-DPR (e.g. mobile) displays\n        const width = cssWidth * dpr;\n        const height = cssHeight * dpr;\n        canvas.width = width;\n        canvas.height = height;\n        const gl = canvas.getContext(\"webgl\");\n        if (!gl) {\n            console.error(\"WebGL not supported\");\n            return;\n        }\n        gl.viewport(0, 0, canvas.width, canvas.height); // Set viewport for DPR\n        console.log(\"WebGL context created successfully\");\n        const mouse = {\n            x: 0,\n            y: 0\n        };\n        const numMetaballs = animationSettings.numMetaballs;\n        const metaballs = [];\n        // Calculate responsive radii based on the smaller dimension, making them larger\n        const baseDimension = Math.min(width, height);\n        const responsiveMinRadius = baseDimension * 0.1; // Doubled from 0.05\n        const responsiveRadiusRange = baseDimension * 0.08; // Doubled from 0.04\n        // Initialize metaballs with configurable settings\n        for(let i = 0; i < numMetaballs; i++){\n            const radius = Math.random() * responsiveRadiusRange + responsiveMinRadius;\n            metaballs.push({\n                x: Math.random() * (width - 2 * radius) + radius,\n                y: Math.random() * (height - 2 * radius) + radius,\n                vx: (Math.random() - 0.5) * animationSettings.speed,\n                vy: (Math.random() - 0.5) * animationSettings.speed,\n                r: radius * 0.75\n            });\n        }\n        const vertexShaderSrc = `\r\n      attribute vec2 position;\r\n      void main() {\r\n        gl_Position = vec4(position, 0.0, 1.0);\r\n      }\r\n    `;\n        const fragmentShaderSrc = `\r\n      precision highp float;\r\n      uniform float u_width;\r\n      uniform float u_height;\r\n      uniform int u_numMetaballs;\r\n      uniform vec3 metaballs[21];\r\n      uniform vec3 u_blueColor;\r\n      uniform vec3 u_whiteColor;\r\n      uniform vec3 u_backgroundColor;\r\n\r\n      void main(){\r\n        float x = gl_FragCoord.x;\r\n        float y = gl_FragCoord.y;\r\n        float sum = 0.0;\r\n\r\n        for (int i = 0; i < u_numMetaballs; i++) {\r\n          vec3 metaball = metaballs[i];\r\n          float dx = metaball.x - x;\r\n          float dy = metaball.y - y;\r\n          float radius = metaball.z;\r\n          sum += (radius * radius) / (dx * dx + dy * dy);\r\n        }\r\n\r\n        float threshold = 0.5;\r\n        float edgeWidth = 0.02;\r\n\r\n        if (sum >= threshold - edgeWidth) {\r\n          float intensity = min(1.0, (sum - threshold) * 20.0);\r\n          vec3 color = mix(u_blueColor, u_whiteColor, intensity);\r\n          float alpha = smoothstep(threshold - edgeWidth, threshold + edgeWidth, sum);\r\n          gl_FragColor = vec4(color, alpha);\r\n          return;\r\n        }\r\n\r\n        gl_FragColor = vec4(u_backgroundColor, 1.0);\r\n      }\r\n    `;\n        // Helper functions for WebGL\n        const compileShader = (gl, shaderSource, shaderType)=>{\n            const shader = gl.createShader(shaderType);\n            if (!shader) throw new Error(\"Could not create shader\");\n            gl.shaderSource(shader, shaderSource);\n            gl.compileShader(shader);\n            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {\n                throw new Error(\"Shader compile failed with: \" + gl.getShaderInfoLog(shader));\n            }\n            return shader;\n        };\n        const getUniformLocation = (gl, program, name)=>{\n            const uniformLocation = gl.getUniformLocation(program, name);\n            if (uniformLocation === -1) {\n                throw new Error(\"Can not find uniform \" + name);\n            }\n            return uniformLocation;\n        };\n        const getAttribLocation = (gl, program, name)=>{\n            const attributeLocation = gl.getAttribLocation(program, name);\n            if (attributeLocation === -1) {\n                throw new Error(\"Can not find attribute \" + name);\n            }\n            return attributeLocation;\n        };\n        const vertexShader = compileShader(gl, vertexShaderSrc, gl.VERTEX_SHADER);\n        const fragmentShader = compileShader(gl, fragmentShaderSrc, gl.FRAGMENT_SHADER);\n        const program = gl.createProgram();\n        if (!program) throw new Error(\"Could not create program\");\n        gl.attachShader(program, vertexShader);\n        gl.attachShader(program, fragmentShader);\n        gl.linkProgram(program);\n        const vertexData = new Float32Array([\n            -1.0,\n            1.0,\n            -1.0,\n            -1.0,\n            1.0,\n            1.0,\n            1.0,\n            -1.0\n        ]);\n        const vertexDataBuffer = gl.createBuffer();\n        gl.bindBuffer(gl.ARRAY_BUFFER, vertexDataBuffer);\n        gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW);\n        const positionHandle = getAttribLocation(gl, program, \"position\");\n        gl.enableVertexAttribArray(positionHandle);\n        gl.vertexAttribPointer(positionHandle, 2, gl.FLOAT, false, 2 * 4, 0);\n        const metaballsHandle = getUniformLocation(gl, program, \"metaballs\");\n        const numMetaballsHandle = getUniformLocation(gl, program, \"u_numMetaballs\");\n        const widthHandle = getUniformLocation(gl, program, \"u_width\");\n        const heightHandle = getUniformLocation(gl, program, \"u_height\");\n        const blueColorHandle = getUniformLocation(gl, program, \"u_blueColor\");\n        const whiteColorHandle = getUniformLocation(gl, program, \"u_whiteColor\");\n        const backgroundColorHandle = getUniformLocation(gl, program, \"u_backgroundColor\");\n        const loop = ()=>{\n            // Get subtle scroll physics effect\n            const scrollEffect = scrollPhysicsRef.current.effect;\n            const time = Date.now() * 0.001 // Convert to seconds\n            ;\n            // Update metaballs with continuous movement and scroll-responsive effects\n            for(let i = 0; i < numMetaballs; i++){\n                const metaball = metaballs[i];\n                // Base continuous movement - always active (slowed down)\n                const baseSpeed = animationSettings.speed * 0.077 // 15% of original speed as base (half of previous)\n                ;\n                const staticWiggleX = Math.sin(time * 0.25 + i * 1.2) * baseSpeed * 0.4 // Half frequency\n                ;\n                const staticWiggleY = Math.cos(time * 0.15 + i * 0.8) * baseSpeed * 0.3 // Half frequency\n                ;\n                // Apply base movement with static wiggle (reduced intensity)\n                let newVx = metaball.vx + staticWiggleX * 0.06;\n                let newVy = metaball.vy + staticWiggleY * 0.05;\n                // Add scroll physics on top of base movement\n                if (Math.abs(scrollEffect) > 0.01) {\n                    // Subtle vertical drift based on scroll direction\n                    newVy += scrollEffect * 0.25;\n                    // Enhanced horizontal wiggle during scroll (slowed down)\n                    const scrollWiggle = Math.sin(time * 1 + i * 0.7) * Math.abs(scrollEffect) * 0.1 // Half frequency\n                    ;\n                    newVx += scrollWiggle;\n                    // Tiny bit of randomness for natural variation\n                    newVx += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.05;\n                    newVy += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.03;\n                }\n                // Ensure minimum movement to prevent stagnation\n                const minSpeed = 0.2;\n                if (Math.abs(newVx) < minSpeed) newVx += (Math.random() - 0.5) * minSpeed;\n                if (Math.abs(newVy) < minSpeed) newVy += (Math.random() - 0.5) * minSpeed;\n                // Update position with modified velocities\n                metaball.x += newVx;\n                metaball.y += newVy;\n                // Store back the velocities with less aggressive damping\n                metaball.vx = newVx * 0.995 // Less damping to maintain movement\n                ;\n                metaball.vy = newVy * 0.995;\n                // Boundary collision\n                if (metaball.x < metaball.r || metaball.x > width - metaball.r) metaball.vx *= -1;\n                if (metaball.y < metaball.r || metaball.y > height - metaball.r) metaball.vy *= -1;\n            }\n            const dataToSendToGPU = new Float32Array(3 * numMetaballs);\n            for(let i = 0; i < numMetaballs; i++){\n                const baseIndex = 3 * i;\n                const mb = metaballs[i];\n                dataToSendToGPU[baseIndex + 0] = mb.x;\n                dataToSendToGPU[baseIndex + 1] = mb.y;\n                dataToSendToGPU[baseIndex + 2] = mb.r;\n            }\n            gl.uniform3fv(metaballsHandle, dataToSendToGPU);\n            gl.uniform1i(numMetaballsHandle, numMetaballs); // Set numMetaballs uniform\n            gl.uniform1f(widthHandle, width);\n            gl.uniform1f(heightHandle, height);\n            gl.uniform3f(blueColorHandle, animationSettings.blueColor.r, animationSettings.blueColor.g, animationSettings.blueColor.b);\n            gl.uniform3f(whiteColorHandle, animationSettings.whiteColor.r, animationSettings.whiteColor.g, animationSettings.whiteColor.b);\n            gl.uniform3f(backgroundColorHandle, animationSettings.backgroundColor.r, animationSettings.backgroundColor.g, animationSettings.backgroundColor.b);\n            gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);\n            animationRef.current = requestAnimationFrame(loop);\n        };\n        // Mouse interaction\n        const handleMouseMove = (e)=>{\n            const rect = canvas.getBoundingClientRect();\n            mouse.x = e.clientX - rect.left;\n            mouse.y = e.clientY - rect.top;\n        };\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        // Handle resize\n        const handleResize = ()=>{\n            const newDpr = window.devicePixelRatio || 1;\n            canvas.width = window.innerWidth * newDpr;\n            canvas.height = window.innerHeight * newDpr;\n            gl.viewport(0, 0, canvas.width, canvas.height);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        gl.useProgram(program);\n        loop();\n        return ()=>{\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            canvas.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"min-h-screen bg-white relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    zIndex: 1,\n                    transform: `translateY(${scrollY * 0.3}px)` // Parallax effect\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"fixed inset-0 w-full h-full pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + `bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300 ${scrollY > 50 ? \"shadow-lg\" : \"shadow-sm\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-lg\",\n                                            children: \"F\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-900 font-bold text-xl tracking-tight\",\n                                        children: \"Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Projects\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Curators\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Community\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Docs\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Connect Wallet\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"relative py-20 lg:py-32 min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-6xl mx-auto px-6 text-center relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-display text-gray-900 mb-8 max-w-4xl mx-auto\",\n                            children: [\n                                \"Discover & Support\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        filter: \"drop-shadow(0 0 20px rgba(59, 130, 246, 0.3))\",\n                                        animation: \"web3Pulse 3s ease-in-out infinite\"\n                                    },\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"Web3 Startups\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 mb-12 max-w-4xl mx-auto\",\n                            children: \"Forcefi is a decentralized launchpad that bridges industry experts with Web3 Startups. List your early-stage project, or monetize your skills as a platform curator.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center gap-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JoinCohortSplitButton, {}, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-wrap justify-center items-center gap-4 mt-8 sm:gap-6\",\n                            children: [\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Follow on X\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Read Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5) 70%, rgba(255, 255, 255, 0) 100%)\",\n                    backdropFilter: \"blur(7px)\"\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-16 relative z-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-extrabold text-gray-900 mb-6\",\n                                        children: \"First Cohort Benefits\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: \"As part of Forcefi’s first cohort, you receive a number of benefits that help shape your product and build an audience for your project and services. Here is a breakdown of what you can expect:\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Biweekly feedback sessions on product, marketing, and BD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Contextually relevant product deepdive & advice\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Ecosystem intros and relevant partnership opportunities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Educational materials and discounts on external courses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent support in token grants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Curators\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Intros to projects that require your services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Monetization opportunities through platform participation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Small pool of competitors (only the best are accepted)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Increase your reach and grow your audience\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent token rewards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"section-spacing relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"Three Ways to Participate\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 max-w-2xl mx-auto\",\n                                            children: \"Join our ecosystem as a project, curator, or investor and shape the Web3 future\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Access our vetted network of 70+ Listed Projects to find talent and growth opportunities. Accelerate your project with community expertise.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Learn More →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Curators\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Support promising projects and earn income. Join our network of 200+ Expert Curators making a real impact in Web3.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-purple-600 hover:text-purple-700 hover:bg-purple-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Get Involved →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Investors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Invest in curated projects at the earliest stage. Earn through staking and support innovative Web3 projects. Access exclusive early-stage opportunities backed by our expert curators.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-green-600 hover:text-green-700 hover:bg-green-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Explore Now →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-20 relative z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute inset-0 bg-white/10 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10 mb-24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"shadow-2xl hover:shadow-3xl transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pt-12 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-8 h-8 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Something Big is Coming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                children: \"Stay tuned for Forcefi's token launch, empowering our ecosystem. Details soon!\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-10 h-10 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-2xl font-bold text-gray-900 mt-4\",\n                                                children: \"Join the Waitlist\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 mt-2\",\n                                                children: \"Be the first to know when we launch\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-6\",\n                                        children: \"Be the First to Experience Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Follow our socials and learn more about our platform to benefit as an early-stage user. If you have questions feel free to reach out and our team will get back to you.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-md mx-auto mb-12 px-4 sm:px-0\",\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col sm:flex-row gap-3\",\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"emailInput\",\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"bg-white border-gray-300 focus:border-blue-500 text-gray-900 placeholder:text-gray-500 text-base shadow-sm flex-grow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 whitespace-nowrap font-semibold shadow-lg w-full sm:w-auto\" // Made button full width on mobile\n                                                        ,\n                                                        onClick: ()=>{\n                                                            const emailInput = document.getElementById(\"emailInput\");\n                                                            const email = emailInput.value;\n                                                            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                                                            if (emailRegex.test(email)) {\n                                                                const button = emailInput.nextElementSibling;\n                                                                button.textContent = \"Subscribed\";\n                                                                button.classList.remove(\"bg-blue-600\", \"hover:bg-blue-700\");\n                                                                button.classList.add(\"bg-green-600\", \"hover:bg-green-700\");\n                                                            } else {\n                                                                alert(\"Please enter a valid email address.\");\n                                                            }\n                                                        },\n                                                        children: \"Join the Waitlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gray-900 py-16 relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-7xl mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center p-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/logo.svg\",\n                                                    alt: \"Forcefi Logo\",\n                                                    style: {\n                                                        filter: \"brightness(0) invert(1)\"\n                                                    },\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-2xl\",\n                                                children: \"Forcefi\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Follow on X\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Discord\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Read Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Telegram\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-400 text-sm\",\n                                        children: \"\\xa9 2025 Forcefi. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"dacc1f6cc99453e7\",\n                children: \"@-webkit-keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}@-moz-keyframes web3Pulse{0%,100%{-moz-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-moz-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@-o-keyframes web3Pulse{0%,100%{-o-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-o-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);-moz-transform:scale(1.02);-o-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}.holographic-card.jsx-dacc1f6cc99453e7{width:100%;background:#111;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;position:relative;overflow:hidden;-webkit-border-radius:15px;-moz-border-radius:15px;border-radius:15px;-webkit-transition:all.5s ease;-moz-transition:all.5s ease;-o-transition:all.5s ease;transition:all.5s ease;-webkit-box-shadow:0 10px 20px rgba(0,0,0,.2);-moz-box-shadow:0 10px 20px rgba(0,0,0,.2);box-shadow:0 10px 20px rgba(0,0,0,.2)}.holographic-card.jsx-dacc1f6cc99453e7:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);-moz-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 399,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Badge,badgeVariants auto */ \n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHFCQUFPRiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLDREQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxLQUFLTSxXQUFXLEdBQUc7QUFFbkIsTUFBTUMsMkJBQWFULDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFBSUQsS0FBS0E7UUFBS0YsV0FBV0gsOENBQUVBLENBQUMsaUNBQWlDRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVyRkksV0FBV0QsV0FBVyxHQUFHO0FBRXpCLE1BQU1FLDBCQUFZViw2Q0FBZ0IsQ0FHaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNLO1FBQ0NMLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLHNEQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSyxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUksZ0NBQWtCWiw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNPO1FBQ0NQLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JPLGdCQUFnQkosV0FBVyxHQUFHO0FBRTlCLE1BQU1NLDRCQUFjZCw2Q0FBZ0IsQ0FHbEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLFlBQVlHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWhFUyxZQUFZTixXQUFXLEdBQUc7QUFFMUIsTUFBTU8sMkJBQWFmLDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsOEJBQThCRztRQUMzQyxHQUFHQyxLQUFLOzs7Ozs7QUFHYlUsV0FBV1AsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLy4vY29tcG9uZW50cy91aS9jYXJkLnRzeD9hZDkxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgQ2FyZCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJvdW5kZWQtbGcgYm9yZGVyIGJnLWNhcmQgdGV4dC1jYXJkLWZvcmVncm91bmQgc2hhZG93LXNtXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkLmRpc3BsYXlOYW1lID0gXCJDYXJkXCJcblxuY29uc3QgQ2FyZEhlYWRlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdiByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgcC02XCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbikpXG5DYXJkSGVhZGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkSGVhZGVyXCJcblxuY29uc3QgQ2FyZFRpdGxlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxIZWFkaW5nRWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGgzXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBsZWFkaW5nLW5vbmUgdHJhY2tpbmctdGlnaHRcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRUaXRsZS5kaXNwbGF5TmFtZSA9IFwiQ2FyZFRpdGxlXCJcblxuY29uc3QgQ2FyZERlc2NyaXB0aW9uID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxQYXJhZ3JhcGhFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8cFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRGVzY3JpcHRpb24uZGlzcGxheU5hbWUgPSBcIkNhcmREZXNjcmlwdGlvblwiXG5cbmNvbnN0IENhcmRDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwicC02IHB0LTBcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkNhcmRDb250ZW50LmRpc3BsYXlOYW1lID0gXCJDYXJkQ29udGVudFwiXG5cbmNvbnN0IENhcmRGb290ZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBpdGVtcy1jZW50ZXIgcC02IHB0LTBcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZEZvb3Rlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEZvb3RlclwiXG5cbmV4cG9ydCB7IENhcmQsIENhcmRIZWFkZXIsIENhcmRGb290ZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkQ29udGVudCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkNhcmQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJkaXYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJoMyIsIkNhcmREZXNjcmlwdGlvbiIsInAiLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/cohort-info-modal.tsx":
/*!*********************************************!*\
  !*** ./components/ui/cohort-info-modal.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CohortInfoModal: () => (/* binding */ CohortInfoModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ CohortInfoModal auto */ \n\n\n\n\n\n\n\n\nconst expertiseOptions = [\n    \"Blockchain Development\",\n    \"Smart Contracts\",\n    \"DeFi Protocols\",\n    \"NFT Development\",\n    \"Web3 Marketing\",\n    \"Tokenomics\",\n    \"Community Building\",\n    \"Product Strategy\",\n    \"Fundraising\",\n    \"Legal & Compliance\",\n    \"UI/UX Design\",\n    \"Security Auditing\"\n];\nfunction CohortInfoModal({ isOpen, onCloseAction }) {\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [projectFormData, setProjectFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        website: \"\",\n        chain: \"\",\n        type: \"\",\n        description: \"\",\n        teamMembers: \"\",\n        email: \"\",\n        goals: \"\",\n        twitter: \"\",\n        discord: \"\",\n        telegram: \"\"\n    });\n    const [otherChain, setOtherChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [curatorFormData, setCuratorFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        services: \"\",\n        documents: \"\",\n        availability: \"\",\n        expertise: [],\n        email: \"\",\n        twitter: \"\",\n        linkedin: \"\",\n        discord: \"\"\n    });\n    const resetForm = ()=>{\n        setSelectedRole(null);\n        setCurrentStep(1);\n        setProjectFormData({\n            title: \"\",\n            website: \"\",\n            chain: \"\",\n            type: \"\",\n            description: \"\",\n            teamMembers: \"\",\n            email: \"\",\n            goals: \"\",\n            twitter: \"\",\n            discord: \"\",\n            telegram: \"\"\n        });\n        setCuratorFormData({\n            name: \"\",\n            services: \"\",\n            documents: \"\",\n            availability: \"\",\n            expertise: [],\n            email: \"\",\n            twitter: \"\",\n            linkedin: \"\",\n            discord: \"\"\n        });\n    };\n    const handleClose = ()=>{\n        resetForm();\n        onCloseAction(); // Updated reference\n    };\n    const handleRoleSelect = (role)=>{\n        setSelectedRole(role);\n        setCurrentStep(2);\n    };\n    const handleBack = ()=>{\n        if (currentStep === 2) {\n            setSelectedRole(null);\n            setCurrentStep(1);\n        } else {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleNext = ()=>{\n        setCurrentStep(currentStep + 1);\n    };\n    const handleSubmit = ()=>{\n        const formData = selectedRole === \"project\" ? projectFormData : curatorFormData;\n        console.log(`${selectedRole} application submitted:`, formData);\n        // Here you would typically send the data to your backend\n        setIsSubmitted(true);\n    };\n    const toggleExpertise = (skill)=>{\n        setCuratorFormData((prev)=>({\n                ...prev,\n                expertise: prev.expertise.includes(skill) ? prev.expertise.filter((s)=>s !== skill) : [\n                    ...prev.expertise,\n                    skill\n                ]\n            }));\n    };\n    const getProgressPercentage = ()=>{\n        if (!selectedRole) return 0;\n        return (currentStep - 1) / 3 * 100;\n    };\n    const renderRoleSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Choose your path to shape the future of Web3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-blue-500\",\n                            onClick: ()=>handleRoleSelect(\"project\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Project\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center pb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        className: \"text-gray-600\",\n                                        children: \"Apply with your Web3 project to get expert guidance and community support\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-green-500\",\n                            onClick: ()=>handleRoleSelect(\"curator\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-green-50 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Curator\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center pb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        className: \"text-gray-600\",\n                                        children: \"Share your expertise and earn by mentoring the next generation of Web3 startups\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 142,\n            columnNumber: 5\n        }, this);\n    const renderProjectForm = ()=>{\n        switch(currentStep){\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Basic Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Enter your project name\",\n                                            value: projectFormData.title,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Website URL\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"https://yourproject.com\",\n                                            value: projectFormData.website,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        website: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Blockchain *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: projectFormData.chain,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        chain: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select blockchain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ethereum\",\n                                                    children: \"Ethereum\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"polygon\",\n                                                    children: \"Polygon\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"bsc\",\n                                                    children: \"BSC\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"solana\",\n                                                    children: \"Solana\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        projectFormData.chain === \"other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Please specify\",\n                                            value: otherChain,\n                                            onChange: (e)=>{\n                                                setOtherChain(e.target.value);\n                                                setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    }));\n                                            },\n                                            className: \"mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Type *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: projectFormData.type,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select project type\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"defi\",\n                                                    children: \"DeFi\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"nft\",\n                                                    children: \"NFT\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"gaming\",\n                                                    children: \"Gaming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"infrastructure\",\n                                                    children: \"Infrastructure\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Team & Contact\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Description *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"Describe your project, its goals, and what makes it unique...\",\n                                            value: projectFormData.description,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Team Members\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"List key team members and their roles\",\n                                            value: projectFormData.teamMembers,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        teamMembers: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Contact Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"email\",\n                                            placeholder: \"<EMAIL>\",\n                                            value: projectFormData.email,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        email: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Goals & Social\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Goals *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"What do you hope to achieve through this cohort?\",\n                                            value: projectFormData.goals,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        goals: e.target.value\n                                                    })),\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Twitter\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"@yourproject\",\n                                            value: projectFormData.twitter,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        twitter: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Discord server invite\",\n                                            value: projectFormData.discord,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        discord: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Telegram group/channel\",\n                                            value: projectFormData.telegram,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        telegram: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const renderCuratorForm = ()=>{\n        switch(currentStep){\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Personal Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Full Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Enter your full name\",\n                                            value: curatorFormData.name,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Services Offered *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"Describe the services and expertise you can provide to Web3 projects...\",\n                                            value: curatorFormData.services,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        services: e.target.value\n                                                    })),\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Portfolio/Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Links to your portfolio, resume, or relevant documents\",\n                                            value: curatorFormData.documents,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        documents: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 11\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Availability & Expertise\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Availability *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: curatorFormData.availability,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        availability: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select your availability\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"full-time\",\n                                                    children: \"Full-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"part-time\",\n                                                    children: \"Part-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"weekends\",\n                                                    children: \"Weekends\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"flexible\",\n                                                    children: \"Flexible\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Expertise Areas *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: expertiseOptions.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: curatorFormData.expertise.includes(skill) ? \"default\" : \"outline\",\n                                                    className: `cursor-pointer transition-colors ${curatorFormData.expertise.includes(skill) ? \"bg-blue-600 hover:bg-blue-700\" : \"hover:bg-blue-50\"}`,\n                                                    onClick: ()=>toggleExpertise(skill),\n                                                    children: skill\n                                                }, skill, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 11\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Contact Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"email\",\n                                            placeholder: \"<EMAIL>\",\n                                            value: curatorFormData.email,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        email: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Twitter\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"@yourusername\",\n                                            value: curatorFormData.twitter,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        twitter: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"LinkedIn\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"LinkedIn profile URL\",\n                                            value: curatorFormData.linkedin,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        linkedin: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Discord username\",\n                                            value: curatorFormData.discord,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        discord: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const renderThankYouMessage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: \"Thank you for applying to Forcefi’s first cohort.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"We will review your submissions and be in touch.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 453,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n        open: isOpen,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                    className: \"relative\",\n                    children: !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                        className: \"text-center\",\n                        children: \"Join The First Cohort\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this),\n                isSubmitted ? renderThankYouMessage() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Step \",\n                                                currentStep - 1,\n                                                \" of 3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                Math.round(getProgressPercentage()),\n                                                \"% Complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: `${getProgressPercentage()}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                !selectedRole && renderRoleSelection(),\n                                selectedRole === \"project\" && renderProjectForm(),\n                                selectedRole === \"curator\" && renderCuratorForm()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBack,\n                                    className: \"flex items-center gap-2 bg-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, this),\n                                currentStep < 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleNext,\n                                    className: \"flex items-center gap-2 bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        \"Next\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSubmit,\n                                    className: \"flex items-center gap-2 bg-green-600 hover:bg-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Submit Application\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 461,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n        lineNumber: 460,\n        columnNumber: 5\n    }, this);\n}\n// Add CSS for the hover effect\nconst JoinWaitlistButton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#\",\n        className: \"rainbow-button\",\n        alt: \"Join the Waitlist\",\n        style: {\n            width: \"calc(20vw + 6px)\",\n            height: \"calc(8vw + 6px)\",\n            backgroundImage: \"linear-gradient(90deg, #00C0FF 0%, #FFCF00 49%, #FC4F4F 80%, #00C0FF 100%)\",\n            borderRadius: \"5px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            textTransform: \"uppercase\",\n            fontSize: \"3vw\",\n            fontWeight: \"bold\",\n            animation: \"slidebg 2s linear infinite\"\n        },\n        children: \"Join the Waitlist\"\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n        lineNumber: 524,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/cohort-info-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 87,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 102,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLG9XQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1ibHVlLTUwMCBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Textarea auto */ \n\n\n\n\nconst textareaVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-background\",\n            destructive: \"border-destructive bg-destructive text-destructive-foreground shadow-sm placeholder:text-muted-foreground hover:border-destructive/40\"\n        },\n        size: {\n            sm: \"h-9 rounded-md\",\n            lg: \"h-11 rounded-md px-4\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"sm\"\n    }\n});\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"textarea\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(textareaVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"18636565857b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvLi9hcHAvZ2xvYmFscy5jc3M/OTAyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE4NjM2NTY1ODU3YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Forcefi - Shape the Future of Web3\",\n    description: \"Forcefi is your decentralized launchpad, uniting startups with expert curators. Launch your vision or join our community today.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7WUFBRVEsd0JBQXdCO3NCQUFFSDs7Ozs7Ozs7Ozs7QUFHbEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRm9yY2VmaSAtIFNoYXBlIHRoZSBGdXR1cmUgb2YgV2ViMycsXG4gIGRlc2NyaXB0aW9uOiAnRm9yY2VmaSBpcyB5b3VyIGRlY2VudHJhbGl6ZWQgbGF1bmNocGFkLCB1bml0aW5nIHN0YXJ0dXBzIHdpdGggZXhwZXJ0IGN1cmF0b3JzLiBMYXVuY2ggeW91ciB2aXNpb24gb3Igam9pbiBvdXIgY29tbXVuaXR5IHRvZGF5LicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfSBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`G:\Projects\New folder (3)\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`G:\Projects\New folder (3)\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/react-remove-scroll","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/styled-jsx","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/get-nonce","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();