/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBZ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvPzRmOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxQcm9qZWN0c1xcXFxOZXcgZm9sZGVyICgzKVxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CProjects%5C%5CNew%20folder%20(3)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/cohort-info-modal */ \"(ssr)/./components/ui/cohort-info-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction JoinCohortSplitButton() {\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [resetForm, setResetForm] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const handleSplit = ()=>{\n        setIsModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedOption(null);\n        setResetForm(true);\n        resetTheForm();\n    };\n    const resetTheForm = ()=>{\n        setResetForm(false);\n    };\n    const buttons = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: \"Project\",\n            action: \"Project\",\n            tooltip: \"Join as a project - Get feedback, ecosystem intros, and milestone rewards\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: \"Curator\",\n            action: \"Curator\",\n            tooltip: \"Join as a curator - Get project intros, monetization opportunities, and rewards\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex items-center justify-center h-20 mx-auto\",\n        children: [\n            \" \",\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__.CohortInfoModal, {\n                isOpen: isModalOpen,\n                onCloseAction: handleCloseModal\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: handleSplit,\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 text-lg md:px-12 md:py-5 md:text-xl font-bold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-opacity duration-300 max-w-xs sm:max-w-sm\",\n                children: [\n                    \" \",\n                    \"Join The First Cohort\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-5 h-5 ml-2 md:w-6 md:h-6 md:ml-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\nfunction HomePage() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    // Subtle scroll physics tracking\n    const scrollPhysicsRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)({\n        velocity: 0,\n        lastY: 0,\n        lastTime: 0,\n        effect: 0\n    });\n    // Fixed optimal animation settings\n    const animationSettings = {\n        numMetaballs: 21,\n        edgeWidth: 0.02,\n        speed: 2.1,\n        threshold: 1.14,\n        intensity: 12.5,\n        blueColor: {\n            r: 0.23,\n            g: 0.51,\n            b: 0.96\n        },\n        whiteColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        },\n        backgroundColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        }\n    };\n    // Handle scroll with subtle physics\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const currentTime = performance.now();\n            const currentY = window.scrollY;\n            const physics = scrollPhysicsRef.current;\n            // Calculate velocity only if we have previous data\n            if (physics.lastTime > 0) {\n                const deltaTime = currentTime - physics.lastTime;\n                const deltaY = currentY - physics.lastY;\n                if (deltaTime > 0) {\n                    // Smooth velocity calculation\n                    const newVelocity = deltaY / deltaTime;\n                    physics.velocity = physics.velocity * 0.7 + newVelocity * 0.3 // Smooth averaging\n                    ;\n                    // Create subtle effect based on velocity\n                    const maxEffect = 0.2 // Very subtle maximum effect\n                    ;\n                    physics.effect = Math.max(-maxEffect, Math.min(maxEffect, physics.velocity * 0.1));\n                }\n            }\n            physics.lastTime = currentTime;\n            physics.lastY = currentY;\n            setScrollY(currentY);\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Gentle decay of scroll effects\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const decayInterval = setInterval(()=>{\n            const physics = scrollPhysicsRef.current;\n            physics.effect *= 0.88 // Very gentle decay\n            ;\n            physics.velocity *= 0.95;\n        }, 16);\n        return ()=>clearInterval(decayInterval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const dpr = window.devicePixelRatio || 1;\n        const cssWidth = window.innerWidth;\n        const cssHeight = window.innerHeight;\n        // Use device-pixel–scaled dimensions for all WebGL maths so the effect fills the entire screen on high-DPR (e.g. mobile) displays\n        const width = cssWidth * dpr;\n        const height = cssHeight * dpr;\n        canvas.width = width;\n        canvas.height = height;\n        const gl = canvas.getContext(\"webgl\");\n        if (!gl) {\n            console.error(\"WebGL not supported\");\n            return;\n        }\n        gl.viewport(0, 0, canvas.width, canvas.height); // Set viewport for DPR\n        console.log(\"WebGL context created successfully\");\n        const mouse = {\n            x: 0,\n            y: 0\n        };\n        const numMetaballs = animationSettings.numMetaballs;\n        const metaballs = [];\n        // Calculate responsive radii based on the smaller dimension, making them larger\n        const baseDimension = Math.min(width, height);\n        const responsiveMinRadius = baseDimension * 0.1; // Doubled from 0.05\n        const responsiveRadiusRange = baseDimension * 0.08; // Doubled from 0.04\n        // Initialize metaballs with configurable settings\n        for(let i = 0; i < numMetaballs; i++){\n            const radius = Math.random() * responsiveRadiusRange + responsiveMinRadius;\n            metaballs.push({\n                x: Math.random() * (width - 2 * radius) + radius,\n                y: Math.random() * (height - 2 * radius) + radius,\n                vx: (Math.random() - 0.5) * animationSettings.speed,\n                vy: (Math.random() - 0.5) * animationSettings.speed,\n                r: radius * 0.75\n            });\n        }\n        const vertexShaderSrc = `\r\n      attribute vec2 position;\r\n      void main() {\r\n        gl_Position = vec4(position, 0.0, 1.0);\r\n      }\r\n    `;\n        // Helper functions for WebGL\n        const compileShader = (gl, shaderSource, shaderType)=>{\n            const shader = gl.createShader(shaderType);\n            if (!shader) throw new Error(\"Could not create shader\");\n            gl.shaderSource(shader, shaderSource);\n            gl.compileShader(shader);\n            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {\n                throw new Error(\"Shader compile failed with: \" + gl.getShaderInfoLog(shader));\n            }\n            return shader;\n        };\n        const getUniformLocation = (gl, program, name)=>{\n            const uniformLocation = gl.getUniformLocation(program, name);\n            if (uniformLocation === -1) {\n                throw new Error(\"Can not find uniform \" + name);\n            }\n            return uniformLocation;\n        };\n        const getAttribLocation = (gl, program, name)=>{\n            const attributeLocation = gl.getAttribLocation(program, name);\n            if (attributeLocation === -1) {\n                throw new Error(\"Can not find attribute \" + name);\n            }\n            return attributeLocation;\n        };\n        const vertexShader = compileShader(gl, vertexShaderSrc, gl.VERTEX_SHADER);\n        const fragmentShader = compileShader(gl, fragmentShaderSrc, gl.FRAGMENT_SHADER);\n        const program = gl.createProgram();\n        if (!program) throw new Error(\"Could not create program\");\n        gl.attachShader(program, vertexShader);\n        gl.attachShader(program, fragmentShader);\n        gl.linkProgram(program);\n        const vertexData = new Float32Array([\n            -1.0,\n            1.0,\n            -1.0,\n            -1.0,\n            1.0,\n            1.0,\n            1.0,\n            -1.0\n        ]);\n        const vertexDataBuffer = gl.createBuffer();\n        gl.bindBuffer(gl.ARRAY_BUFFER, vertexDataBuffer);\n        gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW);\n        const positionHandle = getAttribLocation(gl, program, \"position\");\n        gl.enableVertexAttribArray(positionHandle);\n        gl.vertexAttribPointer(positionHandle, 2, gl.FLOAT, false, 2 * 4, 0);\n        const metaballsHandle = getUniformLocation(gl, program, \"metaballs\");\n        const numMetaballsHandle = getUniformLocation(gl, program, \"u_numMetaballs\");\n        const blueColorHandle = getUniformLocation(gl, program, \"u_blueColor\");\n        const whiteColorHandle = getUniformLocation(gl, program, \"u_whiteColor\");\n        const backgroundColorHandle = getUniformLocation(gl, program, \"u_backgroundColor\");\n        const loop = ()=>{\n            // Get subtle scroll physics effect\n            const scrollEffect = scrollPhysicsRef.current.effect;\n            const time = Date.now() * 0.001 // Convert to seconds\n            ;\n            // Update metaballs with continuous movement and scroll-responsive effects\n            for(let i = 0; i < numMetaballs; i++){\n                const metaball = metaballs[i];\n                // Base continuous movement - always active (slowed down)\n                const baseSpeed = animationSettings.speed * 0.077 // 15% of original speed as base (half of previous)\n                ;\n                const staticWiggleX = Math.sin(time * 0.25 + i * 1.2) * baseSpeed * 0.4 // Half frequency\n                ;\n                const staticWiggleY = Math.cos(time * 0.15 + i * 0.8) * baseSpeed * 0.3 // Half frequency\n                ;\n                // Apply base movement with static wiggle (reduced intensity)\n                let newVx = metaball.vx + staticWiggleX * 0.06;\n                let newVy = metaball.vy + staticWiggleY * 0.05;\n                // Add scroll physics on top of base movement\n                if (Math.abs(scrollEffect) > 0.01) {\n                    // Subtle vertical drift based on scroll direction\n                    newVy += scrollEffect * 0.25;\n                    // Enhanced horizontal wiggle during scroll (slowed down)\n                    const scrollWiggle = Math.sin(time * 1 + i * 0.7) * Math.abs(scrollEffect) * 0.1 // Half frequency\n                    ;\n                    newVx += scrollWiggle;\n                    // Tiny bit of randomness for natural variation\n                    newVx += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.05;\n                    newVy += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.03;\n                }\n                // Ensure minimum movement to prevent stagnation\n                const minSpeed = 0.2;\n                if (Math.abs(newVx) < minSpeed) newVx += (Math.random() - 0.5) * minSpeed;\n                if (Math.abs(newVy) < minSpeed) newVy += (Math.random() - 0.5) * minSpeed;\n                // Update position with modified velocities\n                metaball.x += newVx;\n                metaball.y += newVy;\n                // Store back the velocities with less aggressive damping\n                metaball.vx = newVx * 0.995 // Less damping to maintain movement\n                ;\n                metaball.vy = newVy * 0.995;\n                // Boundary collision\n                if (metaball.x < metaball.r || metaball.x > width - metaball.r) metaball.vx *= -1;\n                if (metaball.y < metaball.r || metaball.y > height - metaball.r) metaball.vy *= -1;\n            }\n            const dataToSendToGPU = new Float32Array(3 * numMetaballs);\n            for(let i = 0; i < numMetaballs; i++){\n                const baseIndex = 3 * i;\n                const mb = metaballs[i];\n                dataToSendToGPU[baseIndex + 0] = mb.x;\n                dataToSendToGPU[baseIndex + 1] = mb.y;\n                dataToSendToGPU[baseIndex + 2] = mb.r;\n            }\n            gl.uniform3fv(metaballsHandle, dataToSendToGPU);\n            gl.uniform1i(numMetaballsHandle, numMetaballs); // Set numMetaballs uniform\n            gl.uniform3f(blueColorHandle, animationSettings.blueColor.r, animationSettings.blueColor.g, animationSettings.blueColor.b);\n            gl.uniform3f(whiteColorHandle, animationSettings.whiteColor.r, animationSettings.whiteColor.g, animationSettings.whiteColor.b);\n            gl.uniform3f(backgroundColorHandle, animationSettings.backgroundColor.r, animationSettings.backgroundColor.g, animationSettings.backgroundColor.b);\n            gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);\n            animationRef.current = requestAnimationFrame(loop);\n        };\n        // Mouse interaction\n        const handleMouseMove = (e)=>{\n            const rect = canvas.getBoundingClientRect();\n            mouse.x = e.clientX - rect.left;\n            mouse.y = e.clientY - rect.top;\n        };\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        // Handle resize\n        const handleResize = ()=>{\n            const newDpr = window.devicePixelRatio || 1;\n            canvas.width = window.innerWidth * newDpr;\n            canvas.height = window.innerHeight * newDpr;\n            gl.viewport(0, 0, canvas.width, canvas.height);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        gl.useProgram(program);\n        loop();\n        return ()=>{\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            canvas.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"min-h-screen bg-white relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    zIndex: 1,\n                    transform: `translateY(${scrollY * 0.3}px)` // Parallax effect\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"fixed inset-0 w-full h-full pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + `bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300 ${scrollY > 50 ? \"shadow-lg\" : \"shadow-sm\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-lg\",\n                                            children: \"F\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-900 font-bold text-xl tracking-tight\",\n                                        children: \"Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Projects\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Curators\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Community\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Docs\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Connect Wallet\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"relative py-20 lg:py-32 min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-6xl mx-auto px-6 text-center relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-display text-gray-900 mb-8 max-w-4xl mx-auto\",\n                            children: [\n                                \"Discover & Support\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        filter: \"drop-shadow(0 0 20px rgba(59, 130, 246, 0.3))\",\n                                        animation: \"web3Pulse 3s ease-in-out infinite\"\n                                    },\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"Web3 Startups\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 mb-12 max-w-4xl mx-auto\",\n                            children: \"Forcefi is a decentralized launchpad that bridges industry experts with Web3 Startups. List your early-stage project, or monetize your skills as a platform curator.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center gap-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JoinCohortSplitButton, {}, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-wrap justify-center items-center gap-4 mt-8 sm:gap-6\",\n                            children: [\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Follow on X\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Read Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5) 70%, rgba(255, 255, 255, 0) 100%)\",\n                    backdropFilter: \"blur(7px)\"\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-16 relative z-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-extrabold text-gray-900 mb-6\",\n                                        children: \"First Cohort Benefits\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: \"As part of Forcefi’s first cohort, you receive a number of benefits that help shape your product and build an audience for your project and services. Here is a breakdown of what you can expect:\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Biweekly feedback sessions on product, marketing, and BD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Contextually relevant product deepdive & advice\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Ecosystem intros and relevant partnership opportunities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Educational materials and discounts on external courses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent support in token grants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Curators\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Intros to projects that require your services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Monetization opportunities through platform participation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Small pool of competitors (only the best are accepted)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Increase your reach and grow your audience\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent token rewards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"section-spacing relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"Three Ways to Participate\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 max-w-2xl mx-auto\",\n                                            children: \"Join our ecosystem as a project, curator, or investor and shape the Web3 future\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Access our vetted network of 70+ Listed Projects to find talent and growth opportunities. Accelerate your project with community expertise.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Learn More →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Curators\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Support promising projects and earn income. Join our network of 200+ Expert Curators making a real impact in Web3.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-purple-600 hover:text-purple-700 hover:bg-purple-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Get Involved →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Investors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Invest in curated projects at the earliest stage. Earn through staking and support innovative Web3 projects. Access exclusive early-stage opportunities backed by our expert curators.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-green-600 hover:text-green-700 hover:bg-green-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Explore Now →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-20 relative z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute inset-0 bg-white/10 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10 mb-24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"shadow-2xl hover:shadow-3xl transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pt-12 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-8 h-8 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Something Big is Coming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                children: \"Stay tuned for Forcefi's token launch, empowering our ecosystem. Details soon!\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-10 h-10 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-2xl font-bold text-gray-900 mt-4\",\n                                                children: \"Join the Waitlist\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 mt-2\",\n                                                children: \"Be the first to know when we launch\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-6\",\n                                        children: \"Be the First to Experience Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Follow our socials and learn more about our platform to benefit as an early-stage user. If you have questions feel free to reach out and our team will get back to you.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-md mx-auto mb-12 px-4 sm:px-0\",\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col sm:flex-row gap-3\",\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"emailInput\",\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"bg-white border-gray-300 focus:border-blue-500 text-gray-900 placeholder:text-gray-500 text-base shadow-sm flex-grow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 whitespace-nowrap font-semibold shadow-lg w-full sm:w-auto\" // Made button full width on mobile\n                                                        ,\n                                                        onClick: ()=>{\n                                                            const emailInput = document.getElementById(\"emailInput\");\n                                                            const email = emailInput.value;\n                                                            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                                                            if (emailRegex.test(email)) {\n                                                                const button = emailInput.nextElementSibling;\n                                                                button.textContent = \"Subscribed\";\n                                                                button.classList.remove(\"bg-blue-600\", \"hover:bg-blue-700\");\n                                                                button.classList.add(\"bg-green-600\", \"hover:bg-green-700\");\n                                                            } else {\n                                                                alert(\"Please enter a valid email address.\");\n                                                            }\n                                                        },\n                                                        children: \"Join the Waitlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gray-900 py-16 relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-7xl mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center p-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/logo.svg\",\n                                                    alt: \"Forcefi Logo\",\n                                                    style: {\n                                                        filter: \"brightness(0) invert(1)\"\n                                                    },\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-2xl\",\n                                                children: \"Forcefi\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Follow on X\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Discord\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Read Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Telegram\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-400 text-sm\",\n                                        children: \"\\xa9 2025 Forcefi. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"dacc1f6cc99453e7\",\n                children: \"@-webkit-keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}@-moz-keyframes web3Pulse{0%,100%{-moz-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-moz-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@-o-keyframes web3Pulse{0%,100%{-o-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-o-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);-moz-transform:scale(1.02);-o-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}.holographic-card.jsx-dacc1f6cc99453e7{width:100%;background:#111;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;position:relative;overflow:hidden;-webkit-border-radius:15px;-moz-border-radius:15px;border-radius:15px;-webkit-transition:all.5s ease;-moz-transition:all.5s ease;-o-transition:all.5s ease;transition:all.5s ease;-webkit-box-shadow:0 10px 20px rgba(0,0,0,.2);-moz-box-shadow:0 10px 20px rgba(0,0,0,.2);box-shadow:0 10px 20px rgba(0,0,0,.2)}.holographic-card.jsx-dacc1f6cc99453e7:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);-moz-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Badge,badgeVariants auto */ \n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/cohort-info-modal.tsx":
/*!*********************************************!*\
  !*** ./components/ui/cohort-info-modal.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CohortInfoModal: () => (/* binding */ CohortInfoModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ CohortInfoModal auto */ \n\n\n\n\n\n\n\n\nconst expertiseOptions = [\n    \"Blockchain Development\",\n    \"Smart Contracts\",\n    \"DeFi Protocols\",\n    \"NFT Development\",\n    \"Web3 Marketing\",\n    \"Tokenomics\",\n    \"Community Building\",\n    \"Product Strategy\",\n    \"Fundraising\",\n    \"Legal & Compliance\",\n    \"UI/UX Design\",\n    \"Security Auditing\"\n];\nfunction CohortInfoModal({ isOpen, onCloseAction }) {\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [projectFormData, setProjectFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        website: \"\",\n        chain: \"\",\n        type: \"\",\n        description: \"\",\n        teamMembers: \"\",\n        email: \"\",\n        goals: \"\",\n        twitter: \"\",\n        discord: \"\",\n        telegram: \"\"\n    });\n    const [otherChain, setOtherChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [curatorFormData, setCuratorFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        services: \"\",\n        documents: \"\",\n        availability: \"\",\n        expertise: [],\n        email: \"\",\n        twitter: \"\",\n        linkedin: \"\",\n        discord: \"\"\n    });\n    const resetForm = ()=>{\n        setSelectedRole(null);\n        setCurrentStep(1);\n        setProjectFormData({\n            title: \"\",\n            website: \"\",\n            chain: \"\",\n            type: \"\",\n            description: \"\",\n            teamMembers: \"\",\n            email: \"\",\n            goals: \"\",\n            twitter: \"\",\n            discord: \"\",\n            telegram: \"\"\n        });\n        setCuratorFormData({\n            name: \"\",\n            services: \"\",\n            documents: \"\",\n            availability: \"\",\n            expertise: [],\n            email: \"\",\n            twitter: \"\",\n            linkedin: \"\",\n            discord: \"\"\n        });\n    };\n    const handleClose = ()=>{\n        resetForm();\n        onCloseAction(); // Updated reference\n    };\n    const handleRoleSelect = (role)=>{\n        setSelectedRole(role);\n        setCurrentStep(2);\n    };\n    const handleBack = ()=>{\n        if (currentStep === 2) {\n            setSelectedRole(null);\n            setCurrentStep(1);\n        } else {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleNext = ()=>{\n        setCurrentStep(currentStep + 1);\n    };\n    const handleSubmit = ()=>{\n        const formData = selectedRole === \"project\" ? projectFormData : curatorFormData;\n        console.log(`${selectedRole} application submitted:`, formData);\n        // Here you would typically send the data to your backend\n        setIsSubmitted(true);\n    };\n    const toggleExpertise = (skill)=>{\n        setCuratorFormData((prev)=>({\n                ...prev,\n                expertise: prev.expertise.includes(skill) ? prev.expertise.filter((s)=>s !== skill) : [\n                    ...prev.expertise,\n                    skill\n                ]\n            }));\n    };\n    const getProgressPercentage = ()=>{\n        if (!selectedRole) return 0;\n        return (currentStep - 1) / 3 * 100;\n    };\n    const renderRoleSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Choose your path to shape the future of Web3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-blue-500\",\n                            onClick: ()=>handleRoleSelect(\"project\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Project\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center pb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        className: \"text-gray-600\",\n                                        children: \"Apply with your Web3 project to get expert guidance and community support\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-green-500\",\n                            onClick: ()=>handleRoleSelect(\"curator\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-green-50 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Curator\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center pb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        className: \"text-gray-600\",\n                                        children: \"Share your expertise and earn by mentoring the next generation of Web3 startups\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 142,\n            columnNumber: 5\n        }, this);\n    const renderProjectForm = ()=>{\n        switch(currentStep){\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Basic Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Enter your project name\",\n                                            value: projectFormData.title,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Website URL\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"https://yourproject.com\",\n                                            value: projectFormData.website,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        website: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Blockchain *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: projectFormData.chain,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        chain: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select blockchain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ethereum\",\n                                                    children: \"Ethereum\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"polygon\",\n                                                    children: \"Polygon\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"bsc\",\n                                                    children: \"BSC\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"solana\",\n                                                    children: \"Solana\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        projectFormData.chain === \"other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Please specify\",\n                                            value: otherChain,\n                                            onChange: (e)=>{\n                                                setOtherChain(e.target.value);\n                                                setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    }));\n                                            },\n                                            className: \"mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Type *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: projectFormData.type,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select project type\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"defi\",\n                                                    children: \"DeFi\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"nft\",\n                                                    children: \"NFT\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"gaming\",\n                                                    children: \"Gaming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"infrastructure\",\n                                                    children: \"Infrastructure\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Team & Contact\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Description *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"Describe your project, its goals, and what makes it unique...\",\n                                            value: projectFormData.description,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Team Members\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"List key team members and their roles\",\n                                            value: projectFormData.teamMembers,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        teamMembers: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Contact Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"email\",\n                                            placeholder: \"<EMAIL>\",\n                                            value: projectFormData.email,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        email: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Goals & Social\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Project Goals *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"What do you hope to achieve through this cohort?\",\n                                            value: projectFormData.goals,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        goals: e.target.value\n                                                    })),\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Twitter\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"@yourproject\",\n                                            value: projectFormData.twitter,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        twitter: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Discord server invite\",\n                                            value: projectFormData.discord,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        discord: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Telegram group/channel\",\n                                            value: projectFormData.telegram,\n                                            onChange: (e)=>setProjectFormData((prev)=>({\n                                                        ...prev,\n                                                        telegram: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const renderCuratorForm = ()=>{\n        switch(currentStep){\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Personal Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Full Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Enter your full name\",\n                                            value: curatorFormData.name,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Services Offered *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            placeholder: \"Describe the services and expertise you can provide to Web3 projects...\",\n                                            value: curatorFormData.services,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        services: e.target.value\n                                                    })),\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Portfolio/Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Links to your portfolio, resume, or relevant documents\",\n                                            value: curatorFormData.documents,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        documents: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 11\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Availability & Expertise\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Availability *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: curatorFormData.availability,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        availability: e.target.value\n                                                    })),\n                                            className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select your availability\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"full-time\",\n                                                    children: \"Full-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"part-time\",\n                                                    children: \"Part-time\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"weekends\",\n                                                    children: \"Weekends\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"flexible\",\n                                                    children: \"Flexible\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Expertise Areas *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: expertiseOptions.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: curatorFormData.expertise.includes(skill) ? \"default\" : \"outline\",\n                                                    className: `cursor-pointer transition-colors ${curatorFormData.expertise.includes(skill) ? \"bg-blue-600 hover:bg-blue-700\" : \"hover:bg-blue-50\"}`,\n                                                    onClick: ()=>toggleExpertise(skill),\n                                                    children: skill\n                                                }, skill, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 11\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Contact Information\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"email\",\n                                            placeholder: \"<EMAIL>\",\n                                            value: curatorFormData.email,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        email: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Twitter\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"@yourusername\",\n                                            value: curatorFormData.twitter,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        twitter: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"LinkedIn\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"LinkedIn profile URL\",\n                                            value: curatorFormData.linkedin,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        linkedin: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Discord username\",\n                                            value: curatorFormData.discord,\n                                            onChange: (e)=>setCuratorFormData((prev)=>({\n                                                        ...prev,\n                                                        discord: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const renderThankYouMessage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: \"Thank you for applying to Forcefi’s first cohort.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"We will review your submissions and be in touch.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 453,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n        open: isOpen,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                    className: \"relative\",\n                    children: !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                        className: \"text-center\",\n                        children: \"Join The First Cohort\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this),\n                isSubmitted ? renderThankYouMessage() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Step \",\n                                                currentStep - 1,\n                                                \" of 3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                Math.round(getProgressPercentage()),\n                                                \"% Complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: `${getProgressPercentage()}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                !selectedRole && renderRoleSelection(),\n                                selectedRole === \"project\" && renderProjectForm(),\n                                selectedRole === \"curator\" && renderCuratorForm()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBack,\n                                    className: \"flex items-center gap-2 bg-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, this),\n                                currentStep < 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleNext,\n                                    className: \"flex items-center gap-2 bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        \"Next\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSubmit,\n                                    className: \"flex items-center gap-2 bg-green-600 hover:bg-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Submit Application\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n            lineNumber: 461,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n        lineNumber: 460,\n        columnNumber: 5\n    }, this);\n}\n// Add CSS for the hover effect\nconst JoinWaitlistButton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#\",\n        className: \"rainbow-button\",\n        alt: \"Join the Waitlist\",\n        style: {\n            width: \"calc(20vw + 6px)\",\n            height: \"calc(8vw + 6px)\",\n            backgroundImage: \"linear-gradient(90deg, #00C0FF 0%, #FFCF00 49%, #FC4F4F 80%, #00C0FF 100%)\",\n            borderRadius: \"5px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            textTransform: \"uppercase\",\n            fontSize: \"3vw\",\n            fontWeight: \"bold\",\n            animation: \"slidebg 2s linear infinite\"\n        },\n        children: \"Join the Waitlist\"\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\cohort-info-modal.tsx\",\n        lineNumber: 524,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/cohort-info-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 87,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 102,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLG9XQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1ibHVlLTUwMCBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Textarea auto */ \n\n\n\n\nconst textareaVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-background\",\n            destructive: \"border-destructive bg-destructive text-destructive-foreground shadow-sm placeholder:text-muted-foreground hover:border-destructive/40\"\n        },\n        size: {\n            sm: \"h-9 rounded-md\",\n            lg: \"h-11 rounded-md px-4\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"sm\"\n    }\n});\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"textarea\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(textareaVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yY2VmaS1sYW5kaW5nLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"18636565857b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvLi9hcHAvZ2xvYmFscy5jc3M/OTAyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE4NjM2NTY1ODU3YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Forcefi - Shape the Future of Web3\",\n    description: \"Forcefi is your decentralized launchpad, uniting startups with expert curators. Launch your vision or join our community today.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7WUFBRVEsd0JBQXdCO3NCQUFFSDs7Ozs7Ozs7Ozs7QUFHbEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JjZWZpLWxhbmRpbmcvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRm9yY2VmaSAtIFNoYXBlIHRoZSBGdXR1cmUgb2YgV2ViMycsXG4gIGRlc2NyaXB0aW9uOiAnRm9yY2VmaSBpcyB5b3VyIGRlY2VudHJhbGl6ZWQgbGF1bmNocGFkLCB1bml0aW5nIHN0YXJ0dXBzIHdpdGggZXhwZXJ0IGN1cmF0b3JzLiBMYXVuY2ggeW91ciB2aXNpb24gb3Igam9pbiBvdXIgY29tbXVuaXR5IHRvZGF5LicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfSBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`G:\Projects\New folder (3)\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`G:\Projects\New folder (3)\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/react-remove-scroll","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/styled-jsx","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/get-nonce","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=G%3A%5CProjects%5CNew%20folder%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CProjects%5CNew%20folder%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();