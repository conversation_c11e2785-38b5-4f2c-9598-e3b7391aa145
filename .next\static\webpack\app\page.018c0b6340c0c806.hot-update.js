"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/cohort-info-modal */ \"(app-pages-browser)/./components/ui/cohort-info-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction JoinCohortSplitButton() {\n    _s();\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [resetForm, setResetForm] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const handleSplit = ()=>{\n        setIsModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedOption(null);\n        setResetForm(true);\n        resetTheForm();\n    };\n    const resetTheForm = ()=>{\n        setResetForm(false);\n    };\n    const buttons = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: \"Project\",\n            action: \"Project\",\n            tooltip: \"Join as a project - Get feedback, ecosystem intros, and milestone rewards\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: \"Curator\",\n            action: \"Curator\",\n            tooltip: \"Join as a curator - Get project intros, monetization opportunities, and rewards\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex items-center justify-center h-20 mx-auto\",\n        children: [\n            \" \",\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__.CohortInfoModal, {\n                isOpen: isModalOpen,\n                onCloseAction: handleCloseModal\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: handleSplit,\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 text-lg md:px-12 md:py-5 md:text-xl font-bold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-opacity duration-300 max-w-xs sm:max-w-sm\",\n                children: [\n                    \" \",\n                    \"Join The First Cohort\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-5 h-5 ml-2 md:w-6 md:h-6 md:ml-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(JoinCohortSplitButton, \"vWJn82U/VLxLtXvi4wRnbykUvkQ=\");\n_c = JoinCohortSplitButton;\nfunction HomePage() {\n    _s1();\n    var _s = $RefreshSig$();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    // Subtle scroll physics tracking\n    const scrollPhysicsRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)({\n        velocity: 0,\n        lastY: 0,\n        lastTime: 0,\n        effect: 0\n    });\n    // Fixed optimal animation settings\n    const animationSettings = {\n        numMetaballs: 21,\n        edgeWidth: 0.02,\n        speed: 2.1,\n        threshold: 1.14,\n        intensity: 12.5,\n        blueColor: {\n            r: 0.23,\n            g: 0.51,\n            b: 0.96\n        },\n        whiteColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        },\n        backgroundColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        }\n    };\n    // Handle scroll with subtle physics\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const currentTime = performance.now();\n            const currentY = window.scrollY;\n            const physics = scrollPhysicsRef.current;\n            // Calculate velocity only if we have previous data\n            if (physics.lastTime > 0) {\n                const deltaTime = currentTime - physics.lastTime;\n                const deltaY = currentY - physics.lastY;\n                if (deltaTime > 0) {\n                    // Smooth velocity calculation\n                    const newVelocity = deltaY / deltaTime;\n                    physics.velocity = physics.velocity * 0.7 + newVelocity * 0.3 // Smooth averaging\n                    ;\n                    // Create subtle effect based on velocity\n                    const maxEffect = 0.2 // Very subtle maximum effect\n                    ;\n                    physics.effect = Math.max(-maxEffect, Math.min(maxEffect, physics.velocity * 0.1));\n                }\n            }\n            physics.lastTime = currentTime;\n            physics.lastY = currentY;\n            setScrollY(currentY);\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Gentle decay of scroll effects\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const decayInterval = setInterval(()=>{\n            const physics = scrollPhysicsRef.current;\n            physics.effect *= 0.88 // Very gentle decay\n            ;\n            physics.velocity *= 0.95;\n        }, 16);\n        return ()=>clearInterval(decayInterval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(_s(()=>{\n        _s();\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const dpr = window.devicePixelRatio || 1;\n        const cssWidth = window.innerWidth;\n        const cssHeight = window.innerHeight;\n        // Use device-pixel–scaled dimensions for all WebGL maths so the effect fills the entire screen on high-DPR (e.g. mobile) displays\n        const width = cssWidth * dpr;\n        const height = cssHeight * dpr;\n        canvas.width = width;\n        canvas.height = height;\n        const gl = canvas.getContext(\"webgl\");\n        if (!gl) {\n            console.error(\"WebGL not supported\");\n            return;\n        }\n        gl.viewport(0, 0, canvas.width, canvas.height); // Set viewport for DPR\n        console.log(\"WebGL context created successfully\");\n        const mouse = {\n            x: 0,\n            y: 0\n        };\n        const numMetaballs = animationSettings.numMetaballs;\n        const metaballs = [];\n        // Calculate responsive radii based on the smaller dimension, making them larger\n        const baseDimension = Math.min(width, height);\n        const responsiveMinRadius = baseDimension * 0.1; // Doubled from 0.05\n        const responsiveRadiusRange = baseDimension * 0.08; // Doubled from 0.04\n        // Initialize metaballs with configurable settings\n        for(let i = 0; i < numMetaballs; i++){\n            const radius = Math.random() * responsiveRadiusRange + responsiveMinRadius;\n            metaballs.push({\n                x: Math.random() * (width - 2 * radius) + radius,\n                y: Math.random() * (height - 2 * radius) + radius,\n                vx: (Math.random() - 0.5) * animationSettings.speed,\n                vy: (Math.random() - 0.5) * animationSettings.speed,\n                r: radius * 0.75\n            });\n        }\n        const vertexShaderSrc = \"\\n      attribute vec2 position;\\n      void main() {\\n        gl_Position = vec4(position, 0.0, 1.0);\\n      }\\n    \";\n        const fragmentShaderSrc = \"\\n      precision highp float;\\n      uniform float u_width;\\n      uniform float u_height;\\n      uniform int u_numMetaballs;\\n      uniform vec3 metaballs[21];\\n      uniform vec3 u_blueColor;\\n      uniform vec3 u_whiteColor;\\n      uniform vec3 u_backgroundColor;\\n\\n      void main(){\\n        float x = gl_FragCoord.x;\\n        float y = gl_FragCoord.y;\\n        float sum = 0.0;\\n\\n        for (int i = 0; i < u_numMetaballs; i++) {\\n          vec3 metaball = metaballs[i];\\n          float dx = metaball.x - x;\\n          float dy = metaball.y - y;\\n          float radius = metaball.z;\\n          sum += (radius * radius) / (dx * dx + dy * dy);\\n        }\\n\\n        float threshold = 0.5;\\n        float edgeWidth = 0.02;\\n\\n        if (sum >= threshold - edgeWidth) {\\n          float intensity = min(1.0, (sum - threshold) * 20.0);\\n          vec3 color = mix(u_blueColor, u_whiteColor, intensity);\\n          float alpha = smoothstep(threshold - edgeWidth, threshold + edgeWidth, sum);\\n          gl_FragColor = vec4(color, alpha);\\n          return;\\n        }\\n\\n        gl_FragColor = vec4(u_backgroundColor, 1.0);\\n      }\\n    \";\n        // Helper functions for WebGL\n        const compileShader = (gl, shaderSource, shaderType)=>{\n            const shader = gl.createShader(shaderType);\n            if (!shader) throw new Error(\"Could not create shader\");\n            gl.shaderSource(shader, shaderSource);\n            gl.compileShader(shader);\n            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {\n                throw new Error(\"Shader compile failed with: \" + gl.getShaderInfoLog(shader));\n            }\n            return shader;\n        };\n        const getUniformLocation = (gl, program, name)=>{\n            const uniformLocation = gl.getUniformLocation(program, name);\n            if (uniformLocation === -1) {\n                throw new Error(\"Can not find uniform \" + name);\n            }\n            return uniformLocation;\n        };\n        const getAttribLocation = (gl, program, name)=>{\n            const attributeLocation = gl.getAttribLocation(program, name);\n            if (attributeLocation === -1) {\n                throw new Error(\"Can not find attribute \" + name);\n            }\n            return attributeLocation;\n        };\n        const vertexShader = compileShader(gl, vertexShaderSrc, gl.VERTEX_SHADER);\n        const fragmentShader = compileShader(gl, fragmentShaderSrc, gl.FRAGMENT_SHADER);\n        const program = gl.createProgram();\n        if (!program) throw new Error(\"Could not create program\");\n        gl.attachShader(program, vertexShader);\n        gl.attachShader(program, fragmentShader);\n        gl.linkProgram(program);\n        const vertexData = new Float32Array([\n            -1.0,\n            1.0,\n            -1.0,\n            -1.0,\n            1.0,\n            1.0,\n            1.0,\n            -1.0\n        ]);\n        const vertexDataBuffer = gl.createBuffer();\n        gl.bindBuffer(gl.ARRAY_BUFFER, vertexDataBuffer);\n        gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW);\n        const positionHandle = getAttribLocation(gl, program, \"position\");\n        gl.enableVertexAttribArray(positionHandle);\n        gl.vertexAttribPointer(positionHandle, 2, gl.FLOAT, false, 2 * 4, 0);\n        const metaballsHandle = getUniformLocation(gl, program, \"metaballs\");\n        const numMetaballsHandle = getUniformLocation(gl, program, \"u_numMetaballs\");\n        const blueColorHandle = getUniformLocation(gl, program, \"u_blueColor\");\n        const whiteColorHandle = getUniformLocation(gl, program, \"u_whiteColor\");\n        const backgroundColorHandle = getUniformLocation(gl, program, \"u_backgroundColor\");\n        const loop = ()=>{\n            // Get subtle scroll physics effect\n            const scrollEffect = scrollPhysicsRef.current.effect;\n            const time = Date.now() * 0.001 // Convert to seconds\n            ;\n            // Update metaballs with continuous movement and scroll-responsive effects\n            for(let i = 0; i < numMetaballs; i++){\n                const metaball = metaballs[i];\n                // Base continuous movement - always active (slowed down)\n                const baseSpeed = animationSettings.speed * 0.077 // 15% of original speed as base (half of previous)\n                ;\n                const staticWiggleX = Math.sin(time * 0.25 + i * 1.2) * baseSpeed * 0.4 // Half frequency\n                ;\n                const staticWiggleY = Math.cos(time * 0.15 + i * 0.8) * baseSpeed * 0.3 // Half frequency\n                ;\n                // Apply base movement with static wiggle (reduced intensity)\n                let newVx = metaball.vx + staticWiggleX * 0.06;\n                let newVy = metaball.vy + staticWiggleY * 0.05;\n                // Add scroll physics on top of base movement\n                if (Math.abs(scrollEffect) > 0.01) {\n                    // Subtle vertical drift based on scroll direction\n                    newVy += scrollEffect * 0.25;\n                    // Enhanced horizontal wiggle during scroll (slowed down)\n                    const scrollWiggle = Math.sin(time * 1 + i * 0.7) * Math.abs(scrollEffect) * 0.1 // Half frequency\n                    ;\n                    newVx += scrollWiggle;\n                    // Tiny bit of randomness for natural variation\n                    newVx += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.05;\n                    newVy += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.03;\n                }\n                // Ensure minimum movement to prevent stagnation\n                const minSpeed = 0.2;\n                if (Math.abs(newVx) < minSpeed) newVx += (Math.random() - 0.5) * minSpeed;\n                if (Math.abs(newVy) < minSpeed) newVy += (Math.random() - 0.5) * minSpeed;\n                // Update position with modified velocities\n                metaball.x += newVx;\n                metaball.y += newVy;\n                // Store back the velocities with less aggressive damping\n                metaball.vx = newVx * 0.995 // Less damping to maintain movement\n                ;\n                metaball.vy = newVy * 0.995;\n                // Boundary collision\n                if (metaball.x < metaball.r || metaball.x > width - metaball.r) metaball.vx *= -1;\n                if (metaball.y < metaball.r || metaball.y > height - metaball.r) metaball.vy *= -1;\n            }\n            const dataToSendToGPU = new Float32Array(3 * numMetaballs);\n            for(let i = 0; i < numMetaballs; i++){\n                const baseIndex = 3 * i;\n                const mb = metaballs[i];\n                dataToSendToGPU[baseIndex + 0] = mb.x;\n                dataToSendToGPU[baseIndex + 1] = mb.y;\n                dataToSendToGPU[baseIndex + 2] = mb.r;\n            }\n            gl.uniform3fv(metaballsHandle, dataToSendToGPU);\n            gl.uniform1i(numMetaballsHandle, numMetaballs); // Set numMetaballs uniform\n            gl.uniform3f(blueColorHandle, animationSettings.blueColor.r, animationSettings.blueColor.g, animationSettings.blueColor.b);\n            gl.uniform3f(whiteColorHandle, animationSettings.whiteColor.r, animationSettings.whiteColor.g, animationSettings.whiteColor.b);\n            gl.uniform3f(backgroundColorHandle, animationSettings.backgroundColor.r, animationSettings.backgroundColor.g, animationSettings.backgroundColor.b);\n            gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);\n            animationRef.current = requestAnimationFrame(loop);\n        };\n        // Mouse interaction\n        const handleMouseMove = (e)=>{\n            const rect = canvas.getBoundingClientRect();\n            mouse.x = e.clientX - rect.left;\n            mouse.y = e.clientY - rect.top;\n        };\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        // Handle resize\n        const handleResize = ()=>{\n            const newDpr = window.devicePixelRatio || 1;\n            canvas.width = window.innerWidth * newDpr;\n            canvas.height = window.innerHeight * newDpr;\n            gl.viewport(0, 0, canvas.width, canvas.height);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        gl.useProgram(program);\n        loop();\n        return ()=>{\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            canvas.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, \"ZdQBZ3rq7bWAAMQq6hlVCmYF0jM=\", true), []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"min-h-screen bg-white relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    zIndex: 1,\n                    transform: \"translateY(\".concat(scrollY * 0.3, \"px)\") // Parallax effect\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"fixed inset-0 w-full h-full pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300 \".concat(scrollY > 50 ? \"shadow-lg\" : \"shadow-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-lg\",\n                                            children: \"F\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-900 font-bold text-xl tracking-tight\",\n                                        children: \"Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Projects\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Curators\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Community\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Docs\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Connect Wallet\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"relative py-20 lg:py-32 min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-6xl mx-auto px-6 text-center relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-display text-gray-900 mb-8 max-w-4xl mx-auto\",\n                            children: [\n                                \"Discover & Support\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        filter: \"drop-shadow(0 0 20px rgba(59, 130, 246, 0.3))\",\n                                        animation: \"web3Pulse 3s ease-in-out infinite\"\n                                    },\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"Web3 Startups\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 mb-12 max-w-4xl mx-auto\",\n                            children: \"Forcefi is a decentralized launchpad that bridges industry experts with Web3 Startups. List your early-stage project, or monetize your skills as a platform curator.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center gap-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JoinCohortSplitButton, {}, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-wrap justify-center items-center gap-4 mt-8 sm:gap-6\",\n                            children: [\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Follow on X\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Read Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5) 70%, rgba(255, 255, 255, 0) 100%)\",\n                    backdropFilter: \"blur(7px)\"\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-16 relative z-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-extrabold text-gray-900 mb-6\",\n                                        children: \"First Cohort Benefits\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: \"As part of Forcefi’s first cohort, you receive a number of benefits that help shape your product and build an audience for your project and services. Here is a breakdown of what you can expect:\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Biweekly feedback sessions on product, marketing, and BD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Contextually relevant product deepdive & advice\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Ecosystem intros and relevant partnership opportunities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Educational materials and discounts on external courses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent support in token grants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Curators\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Intros to projects that require your services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Monetization opportunities through platform participation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Small pool of competitors (only the best are accepted)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Increase your reach and grow your audience\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent token rewards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"section-spacing relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"Three Ways to Participate\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 max-w-2xl mx-auto\",\n                                            children: \"Join our ecosystem as a project, curator, or investor and shape the Web3 future\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Access our vetted network of 70+ Listed Projects to find talent and growth opportunities. Accelerate your project with community expertise.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Learn More →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Curators\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Support promising projects and earn income. Join our network of 200+ Expert Curators making a real impact in Web3.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-purple-600 hover:text-purple-700 hover:bg-purple-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Get Involved →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Investors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Invest in curated projects at the earliest stage. Earn through staking and support innovative Web3 projects. Access exclusive early-stage opportunities backed by our expert curators.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-green-600 hover:text-green-700 hover:bg-green-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Explore Now →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-20 relative z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute inset-0 bg-white/10 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10 mb-24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"shadow-2xl hover:shadow-3xl transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pt-12 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-8 h-8 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Something Big is Coming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                children: \"Stay tuned for Forcefi's token launch, empowering our ecosystem. Details soon!\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-10 h-10 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-2xl font-bold text-gray-900 mt-4\",\n                                                children: \"Join the Waitlist\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 mt-2\",\n                                                children: \"Be the first to know when we launch\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-6\",\n                                        children: \"Be the First to Experience Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Follow our socials and learn more about our platform to benefit as an early-stage user. If you have questions feel free to reach out and our team will get back to you.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-md mx-auto mb-12 px-4 sm:px-0\",\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col sm:flex-row gap-3\",\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"emailInput\",\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"bg-white border-gray-300 focus:border-blue-500 text-gray-900 placeholder:text-gray-500 text-base shadow-sm flex-grow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 whitespace-nowrap font-semibold shadow-lg w-full sm:w-auto\" // Made button full width on mobile\n                                                        ,\n                                                        onClick: ()=>{\n                                                            const emailInput = document.getElementById(\"emailInput\");\n                                                            const email = emailInput.value;\n                                                            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                                                            if (emailRegex.test(email)) {\n                                                                const button = emailInput.nextElementSibling;\n                                                                button.textContent = \"Subscribed\";\n                                                                button.classList.remove(\"bg-blue-600\", \"hover:bg-blue-700\");\n                                                                button.classList.add(\"bg-green-600\", \"hover:bg-green-700\");\n                                                            } else {\n                                                                alert(\"Please enter a valid email address.\");\n                                                            }\n                                                        },\n                                                        children: \"Join the Waitlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gray-900 py-16 relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-7xl mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center p-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/logo.svg\",\n                                                    alt: \"Forcefi Logo\",\n                                                    style: {\n                                                        filter: \"brightness(0) invert(1)\"\n                                                    },\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-2xl\",\n                                                children: \"Forcefi\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Follow on X\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Discord\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Read Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Telegram\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-400 text-sm\",\n                                        children: \"\\xa9 2025 Forcefi. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"dacc1f6cc99453e7\",\n                children: \"@-webkit-keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}@-moz-keyframes web3Pulse{0%,100%{-moz-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-moz-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@-o-keyframes web3Pulse{0%,100%{-o-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-o-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);-moz-transform:scale(1.02);-o-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}.holographic-card.jsx-dacc1f6cc99453e7{width:100%;background:#111;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;position:relative;overflow:hidden;-webkit-border-radius:15px;-moz-border-radius:15px;border-radius:15px;-webkit-transition:all.5s ease;-moz-transition:all.5s ease;-o-transition:all.5s ease;transition:all.5s ease;-webkit-box-shadow:0 10px 20px rgba(0,0,0,.2);-moz-box-shadow:0 10px 20px rgba(0,0,0,.2);box-shadow:0 10px 20px rgba(0,0,0,.2)}.holographic-card.jsx-dacc1f6cc99453e7:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);-moz-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 395,\n        columnNumber: 5\n    }, this);\n}\n_s1(HomePage, \"N1ssUxSLH+wq94IyCyxOKQa75x4=\");\n_c1 = HomePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"JoinCohortSplitButton\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});