"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/cohort-info-modal */ \"(app-pages-browser)/./components/ui/cohort-info-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,DollarSign,FileText,MessageCircle,Rocket,Send,Shield,Twitter,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction JoinCohortSplitButton() {\n    _s();\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [resetForm, setResetForm] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const handleSplit = ()=>{\n        setIsModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedOption(null);\n        setResetForm(true);\n        resetTheForm();\n    };\n    const resetTheForm = ()=>{\n        setResetForm(false);\n    };\n    const buttons = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: \"Project\",\n            action: \"Project\",\n            tooltip: \"Join as a project - Get feedback, ecosystem intros, and milestone rewards\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: \"Curator\",\n            action: \"Curator\",\n            tooltip: \"Join as a curator - Get project intros, monetization opportunities, and rewards\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex items-center justify-center h-20 mx-auto\",\n        children: [\n            \" \",\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_cohort_info_modal__WEBPACK_IMPORTED_MODULE_5__.CohortInfoModal, {\n                isOpen: isModalOpen,\n                onCloseAction: handleCloseModal\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: handleSplit,\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 text-lg md:px-12 md:py-5 md:text-xl font-bold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-opacity duration-300 max-w-xs sm:max-w-sm\",\n                children: [\n                    \" \",\n                    \"Join The First Cohort\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-5 h-5 ml-2 md:w-6 md:h-6 md:ml-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(JoinCohortSplitButton, \"vWJn82U/VLxLtXvi4wRnbykUvkQ=\");\n_c = JoinCohortSplitButton;\nfunction HomePage() {\n    _s1();\n    var _s = $RefreshSig$();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    // Subtle scroll physics tracking\n    const scrollPhysicsRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)({\n        velocity: 0,\n        lastY: 0,\n        lastTime: 0,\n        effect: 0\n    });\n    // Fixed optimal animation settings\n    const animationSettings = {\n        numMetaballs: 21,\n        edgeWidth: 0.02,\n        speed: 2.1,\n        threshold: 1.14,\n        intensity: 12.5,\n        blueColor: {\n            r: 0.23,\n            g: 0.51,\n            b: 0.96\n        },\n        whiteColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        },\n        backgroundColor: {\n            r: 1.0,\n            g: 1.0,\n            b: 1.0\n        }\n    };\n    // Handle scroll with subtle physics\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const currentTime = performance.now();\n            const currentY = window.scrollY;\n            const physics = scrollPhysicsRef.current;\n            // Calculate velocity only if we have previous data\n            if (physics.lastTime > 0) {\n                const deltaTime = currentTime - physics.lastTime;\n                const deltaY = currentY - physics.lastY;\n                if (deltaTime > 0) {\n                    // Smooth velocity calculation\n                    const newVelocity = deltaY / deltaTime;\n                    physics.velocity = physics.velocity * 0.7 + newVelocity * 0.3 // Smooth averaging\n                    ;\n                    // Create subtle effect based on velocity\n                    const maxEffect = 0.2 // Very subtle maximum effect\n                    ;\n                    physics.effect = Math.max(-maxEffect, Math.min(maxEffect, physics.velocity * 0.1));\n                }\n            }\n            physics.lastTime = currentTime;\n            physics.lastY = currentY;\n            setScrollY(currentY);\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Gentle decay of scroll effects\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const decayInterval = setInterval(()=>{\n            const physics = scrollPhysicsRef.current;\n            physics.effect *= 0.88 // Very gentle decay\n            ;\n            physics.velocity *= 0.95;\n        }, 16);\n        return ()=>clearInterval(decayInterval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(_s(()=>{\n        _s();\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const dpr = window.devicePixelRatio || 1;\n        const cssWidth = window.innerWidth;\n        const cssHeight = window.innerHeight;\n        // Use device-pixel–scaled dimensions for all WebGL maths so the effect fills the entire screen on high-DPR (e.g. mobile) displays\n        const width = cssWidth * dpr;\n        const height = cssHeight * dpr;\n        canvas.width = width;\n        canvas.height = height;\n        const gl = canvas.getContext(\"webgl\");\n        if (!gl) {\n            console.error(\"WebGL not supported\");\n            return;\n        }\n        gl.viewport(0, 0, canvas.width, canvas.height); // Set viewport for DPR\n        console.log(\"WebGL context created successfully\");\n        const mouse = {\n            x: 0,\n            y: 0\n        };\n        const numMetaballs = animationSettings.numMetaballs;\n        const metaballs = [];\n        // Calculate responsive radii based on the smaller dimension, making them larger\n        const baseDimension = Math.min(width, height);\n        const responsiveMinRadius = baseDimension * 0.1; // Doubled from 0.05\n        const responsiveRadiusRange = baseDimension * 0.08; // Doubled from 0.04\n        // Initialize metaballs with configurable settings\n        for(let i = 0; i < numMetaballs; i++){\n            const radius = Math.random() * responsiveRadiusRange + responsiveMinRadius;\n            metaballs.push({\n                x: Math.random() * (width - 2 * radius) + radius,\n                y: Math.random() * (height - 2 * radius) + radius,\n                vx: (Math.random() - 0.5) * animationSettings.speed,\n                vy: (Math.random() - 0.5) * animationSettings.speed,\n                r: radius * 0.75\n            });\n        }\n        const vertexShaderSrc = \"\\n      attribute vec2 position;\\n      void main() {\\n        gl_Position = vec4(position, 0.0, 1.0);\\n      }\\n    \";\n        const fragmentShaderSrc = \"\\n      precision highp float;\\n      uniform float u_width;\\n      uniform float u_height;\\n      uniform int u_numMetaballs;\\n      uniform vec3 metaballs[21];\\n      uniform vec3 u_blueColor;\\n      uniform vec3 u_whiteColor;\\n      uniform vec3 u_backgroundColor;\\n\\n      void main(){\\n        float x = gl_FragCoord.x;\\n        float y = gl_FragCoord.y;\\n        float sum = 0.0;\\n\\n        for (int i = 0; i < 21; i++) {\\n          if (i >= u_numMetaballs) break;\\n          vec3 metaball = metaballs[i];\\n          float dx = metaball.x - x;\\n          float dy = metaball.y - y;\\n          float radius = metaball.z;\\n          sum += (radius * radius) / (dx * dx + dy * dy);\\n        }\\n\\n        float threshold = 0.5;\\n        float edgeWidth = 0.02;\\n\\n        if (sum >= threshold - edgeWidth) {\\n          float intensity = min(1.0, (sum - threshold) * 20.0);\\n          vec3 color = mix(u_blueColor, u_whiteColor, intensity);\\n          float alpha = smoothstep(threshold - edgeWidth, threshold + edgeWidth, sum);\\n          gl_FragColor = vec4(color, alpha);\\n          return;\\n        }\\n\\n        gl_FragColor = vec4(u_backgroundColor, 1.0);\\n      }\\n    \";\n        // Helper functions for WebGL\n        const compileShader = (gl, shaderSource, shaderType)=>{\n            const shader = gl.createShader(shaderType);\n            if (!shader) throw new Error(\"Could not create shader\");\n            gl.shaderSource(shader, shaderSource);\n            gl.compileShader(shader);\n            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {\n                throw new Error(\"Shader compile failed with: \" + gl.getShaderInfoLog(shader));\n            }\n            return shader;\n        };\n        const getUniformLocation = (gl, program, name)=>{\n            const uniformLocation = gl.getUniformLocation(program, name);\n            if (uniformLocation === -1) {\n                throw new Error(\"Can not find uniform \" + name);\n            }\n            return uniformLocation;\n        };\n        const getAttribLocation = (gl, program, name)=>{\n            const attributeLocation = gl.getAttribLocation(program, name);\n            if (attributeLocation === -1) {\n                throw new Error(\"Can not find attribute \" + name);\n            }\n            return attributeLocation;\n        };\n        const vertexShader = compileShader(gl, vertexShaderSrc, gl.VERTEX_SHADER);\n        const fragmentShader = compileShader(gl, fragmentShaderSrc, gl.FRAGMENT_SHADER);\n        const program = gl.createProgram();\n        if (!program) throw new Error(\"Could not create program\");\n        gl.attachShader(program, vertexShader);\n        gl.attachShader(program, fragmentShader);\n        gl.linkProgram(program);\n        const vertexData = new Float32Array([\n            -1.0,\n            1.0,\n            -1.0,\n            -1.0,\n            1.0,\n            1.0,\n            1.0,\n            -1.0\n        ]);\n        const vertexDataBuffer = gl.createBuffer();\n        gl.bindBuffer(gl.ARRAY_BUFFER, vertexDataBuffer);\n        gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW);\n        const positionHandle = getAttribLocation(gl, program, \"position\");\n        gl.enableVertexAttribArray(positionHandle);\n        gl.vertexAttribPointer(positionHandle, 2, gl.FLOAT, false, 2 * 4, 0);\n        const metaballsHandle = getUniformLocation(gl, program, \"metaballs\");\n        const numMetaballsHandle = getUniformLocation(gl, program, \"u_numMetaballs\");\n        const widthHandle = getUniformLocation(gl, program, \"u_width\");\n        const heightHandle = getUniformLocation(gl, program, \"u_height\");\n        const blueColorHandle = getUniformLocation(gl, program, \"u_blueColor\");\n        const whiteColorHandle = getUniformLocation(gl, program, \"u_whiteColor\");\n        const backgroundColorHandle = getUniformLocation(gl, program, \"u_backgroundColor\");\n        const loop = ()=>{\n            // Get subtle scroll physics effect\n            const scrollEffect = scrollPhysicsRef.current.effect;\n            const time = Date.now() * 0.001 // Convert to seconds\n            ;\n            // Update metaballs with continuous movement and scroll-responsive effects\n            for(let i = 0; i < numMetaballs; i++){\n                const metaball = metaballs[i];\n                // Base continuous movement - always active (slowed down)\n                const baseSpeed = animationSettings.speed * 0.077 // 15% of original speed as base (half of previous)\n                ;\n                const staticWiggleX = Math.sin(time * 0.25 + i * 1.2) * baseSpeed * 0.4 // Half frequency\n                ;\n                const staticWiggleY = Math.cos(time * 0.15 + i * 0.8) * baseSpeed * 0.3 // Half frequency\n                ;\n                // Apply base movement with static wiggle (reduced intensity)\n                let newVx = metaball.vx + staticWiggleX * 0.06;\n                let newVy = metaball.vy + staticWiggleY * 0.05;\n                // Add scroll physics on top of base movement\n                if (Math.abs(scrollEffect) > 0.01) {\n                    // Subtle vertical drift based on scroll direction\n                    newVy += scrollEffect * 0.25;\n                    // Enhanced horizontal wiggle during scroll (slowed down)\n                    const scrollWiggle = Math.sin(time * 1 + i * 0.7) * Math.abs(scrollEffect) * 0.1 // Half frequency\n                    ;\n                    newVx += scrollWiggle;\n                    // Tiny bit of randomness for natural variation\n                    newVx += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.05;\n                    newVy += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.03;\n                }\n                // Ensure minimum movement to prevent stagnation\n                const minSpeed = 0.2;\n                if (Math.abs(newVx) < minSpeed) newVx += (Math.random() - 0.5) * minSpeed;\n                if (Math.abs(newVy) < minSpeed) newVy += (Math.random() - 0.5) * minSpeed;\n                // Update position with modified velocities\n                metaball.x += newVx;\n                metaball.y += newVy;\n                // Store back the velocities with less aggressive damping\n                metaball.vx = newVx * 0.995 // Less damping to maintain movement\n                ;\n                metaball.vy = newVy * 0.995;\n                // Boundary collision\n                if (metaball.x < metaball.r || metaball.x > width - metaball.r) metaball.vx *= -1;\n                if (metaball.y < metaball.r || metaball.y > height - metaball.r) metaball.vy *= -1;\n            }\n            const dataToSendToGPU = new Float32Array(3 * numMetaballs);\n            for(let i = 0; i < numMetaballs; i++){\n                const baseIndex = 3 * i;\n                const mb = metaballs[i];\n                dataToSendToGPU[baseIndex + 0] = mb.x;\n                dataToSendToGPU[baseIndex + 1] = mb.y;\n                dataToSendToGPU[baseIndex + 2] = mb.r;\n            }\n            gl.uniform3fv(metaballsHandle, dataToSendToGPU);\n            gl.uniform1i(numMetaballsHandle, numMetaballs); // Set numMetaballs uniform\n            gl.uniform1f(widthHandle, width);\n            gl.uniform1f(heightHandle, height);\n            gl.uniform3f(blueColorHandle, animationSettings.blueColor.r, animationSettings.blueColor.g, animationSettings.blueColor.b);\n            gl.uniform3f(whiteColorHandle, animationSettings.whiteColor.r, animationSettings.whiteColor.g, animationSettings.whiteColor.b);\n            gl.uniform3f(backgroundColorHandle, animationSettings.backgroundColor.r, animationSettings.backgroundColor.g, animationSettings.backgroundColor.b);\n            gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);\n            animationRef.current = requestAnimationFrame(loop);\n        };\n        // Mouse interaction\n        const handleMouseMove = (e)=>{\n            const rect = canvas.getBoundingClientRect();\n            mouse.x = e.clientX - rect.left;\n            mouse.y = e.clientY - rect.top;\n        };\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        // Handle resize\n        const handleResize = ()=>{\n            const newDpr = window.devicePixelRatio || 1;\n            canvas.width = window.innerWidth * newDpr;\n            canvas.height = window.innerHeight * newDpr;\n            gl.viewport(0, 0, canvas.width, canvas.height);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        gl.useProgram(program);\n        loop();\n        return ()=>{\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            canvas.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, \"ZdQBZ3rq7bWAAMQq6hlVCmYF0jM=\", true), []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"min-h-screen bg-white relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    zIndex: 1,\n                    transform: \"translateY(\".concat(scrollY * 0.3, \"px)\") // Parallax effect\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"fixed inset-0 w-full h-full pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300 \".concat(scrollY > 50 ? \"shadow-lg\" : \"shadow-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-lg\",\n                                            children: \"F\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-900 font-bold text-xl tracking-tight\",\n                                        children: \"Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Projects\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Curators\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Community\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group\",\n                                        children: [\n                                            \"Docs\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Connect Wallet\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"relative py-20 lg:py-32 min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-6xl mx-auto px-6 text-center relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-display text-gray-900 mb-8 max-w-4xl mx-auto\",\n                            children: [\n                                \"Discover & Support\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        filter: \"drop-shadow(0 0 20px rgba(59, 130, 246, 0.3))\",\n                                        animation: \"web3Pulse 3s ease-in-out infinite\"\n                                    },\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"Web3 Startups\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 mb-12 max-w-4xl mx-auto\",\n                            children: \"Forcefi is a decentralized launchpad that bridges industry experts with Web3 Startups. List your early-stage project, or monetize your skills as a platform curator.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center gap-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JoinCohortSplitButton, {}, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-wrap justify-center items-center gap-4 mt-8 sm:gap-6\",\n                            children: [\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Follow on X\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Discord\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Read Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-dacc1f6cc99453e7\",\n                                            children: \"Join Telegram\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5) 70%, rgba(255, 255, 255, 0) 100%)\",\n                    backdropFilter: \"blur(7px)\"\n                },\n                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-16 relative z-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-extrabold text-gray-900 mb-6\",\n                                        children: \"First Cohort Benefits\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: \"As part of Forcefi’s first cohort, you receive a number of benefits that help shape your product and build an audience for your project and services. Here is a breakdown of what you can expect:\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Biweekly feedback sessions on product, marketing, and BD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Contextually relevant product deepdive & advice\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Ecosystem intros and relevant partnership opportunities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Educational materials and discounts on external courses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent support in token grants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-4 pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-2 text-center\",\n                                                        children: \"Curators\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"pb-6 px-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Intros to projects that require your services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Monetization opportunities through platform participation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Small pool of competitors (only the best are accepted)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Increase your reach and grow your audience\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\",\n                                                            children: \"Milestone-dependent token rewards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"section-spacing relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"container-wide\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"Three Ways to Participate\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-body-large text-gray-600 max-w-2xl mx-auto\",\n                                            children: \"Join our ecosystem as a project, curator, or investor and shape the Web3 future\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"grid md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Access our vetted network of 70+ Listed Projects to find talent and growth opportunities. Accelerate your project with community expertise.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Learn More →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Curators\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Support promising projects and earn income. Join our network of 200+ Expert Curators making a real impact in Web3.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-purple-600 hover:text-purple-700 hover:bg-purple-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Get Involved →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    className: \"pb-6 pt-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-10 h-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Investors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"pb-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                            className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                            children: \"Invest in curated projects at the earliest stage. Earn through staking and support innovative Web3 projects. Access exclusive early-stage opportunities backed by our expert curators.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-green-600 hover:text-green-700 hover:bg-green-50 font-semibold text-base px-6 py-3 rounded-lg\",\n                                                            children: \"Explore Now →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"py-20 relative z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"absolute inset-0 bg-white/10 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10 mb-24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"shadow-2xl hover:shadow-3xl transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pt-12 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-8 h-8 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Something Big is Coming\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-600 text-lg leading-relaxed mb-8\",\n                                                children: \"Stay tuned for Forcefi's token launch, empowering our ecosystem. Details soon!\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-4xl mx-auto px-6 text-center relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col items-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-10 h-10 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-2xl font-bold text-gray-900 mt-4\",\n                                                children: \"Join the Waitlist\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-lg text-gray-600 mt-2\",\n                                                children: \"Be the first to know when we launch\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-4xl font-bold text-gray-900 mb-6\",\n                                        children: \"Be the First to Experience Forcefi\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Follow our socials and learn more about our platform to benefit as an early-stage user. If you have questions feel free to reach out and our team will get back to you.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-md mx-auto mb-12 px-4 sm:px-0\",\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col sm:flex-row gap-3\",\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"emailInput\",\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"bg-white border-gray-300 focus:border-blue-500 text-gray-900 placeholder:text-gray-500 text-base shadow-sm flex-grow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 whitespace-nowrap font-semibold shadow-lg w-full sm:w-auto\" // Made button full width on mobile\n                                                        ,\n                                                        onClick: ()=>{\n                                                            const emailInput = document.getElementById(\"emailInput\");\n                                                            const email = emailInput.value;\n                                                            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                                                            if (emailRegex.test(email)) {\n                                                                const button = emailInput.nextElementSibling;\n                                                                button.textContent = \"Subscribed\";\n                                                                button.classList.remove(\"bg-blue-600\", \"hover:bg-blue-700\");\n                                                                button.classList.add(\"bg-green-600\", \"hover:bg-green-700\");\n                                                            } else {\n                                                                alert(\"Please enter a valid email address.\");\n                                                            }\n                                                        },\n                                                        children: \"Join the Waitlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"bg-gray-900 py-16 relative z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"max-w-7xl mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center p-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/logo.svg\",\n                                                    alt: \"Forcefi Logo\",\n                                                    style: {\n                                                        filter: \"brightness(0) invert(1)\"\n                                                    },\n                                                    className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-white font-bold text-2xl\",\n                                                children: \"Forcefi\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Follow on X\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Discord\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Read Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_DollarSign_FileText_MessageCircle_Rocket_Send_Shield_Twitter_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-sm\",\n                                                        children: \"Join Telegram\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dacc1f6cc99453e7\" + \" \" + \"text-gray-400 text-sm\",\n                                        children: \"\\xa9 2025 Forcefi. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                        lineNumber: 691,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"dacc1f6cc99453e7\",\n                children: \"@-webkit-keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}@-moz-keyframes web3Pulse{0%,100%{-moz-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-moz-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@-o-keyframes web3Pulse{0%,100%{-o-transform:scale(1);transform:scale(1);filter:brightness(1.1)}50%{-o-transform:scale(1.02);transform:scale(1.02);filter:brightness(1.3)}}@keyframes web3Pulse{0%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);-webkit-filter:brightness(1.1);filter:brightness(1.1)}50%{-webkit-transform:scale(1.02);-moz-transform:scale(1.02);-o-transform:scale(1.02);transform:scale(1.02);-webkit-filter:brightness(1.3);filter:brightness(1.3)}}.holographic-card.jsx-dacc1f6cc99453e7{width:100%;background:#111;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;position:relative;overflow:hidden;-webkit-border-radius:15px;-moz-border-radius:15px;border-radius:15px;-webkit-transition:all.5s ease;-moz-transition:all.5s ease;-o-transition:all.5s ease;transition:all.5s ease;-webkit-box-shadow:0 10px 20px rgba(0,0,0,.2);-moz-box-shadow:0 10px 20px rgba(0,0,0,.2);box-shadow:0 10px 20px rgba(0,0,0,.2)}.holographic-card.jsx-dacc1f6cc99453e7:hover{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05);-webkit-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);-moz-box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3);box-shadow:0 0 20px rgba(0,255,255,.5),0 10px 20px rgba(0,0,0,.3)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Projects\\\\New folder (3)\\\\app\\\\page.tsx\",\n        lineNumber: 400,\n        columnNumber: 5\n    }, this);\n}\n_s1(HomePage, \"N1ssUxSLH+wq94IyCyxOKQa75x4=\");\n_c1 = HomePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"JoinCohortSplitButton\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUrQztBQUNGO0FBQ21EO0FBQzdCO0FBVzlDO0FBQ087QUFDdUI7QUFFbkQsU0FBU3FCOztJQUNQLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR0gsK0NBQVFBLENBQStCO0lBQ25GLE1BQU0sQ0FBQ0ksYUFBYUMsZUFBZSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNNLFdBQVdDLGFBQWEsR0FBR1AsK0NBQVFBLENBQUM7SUFFM0MsTUFBTVEsY0FBYztRQUNsQkgsZUFBZTtJQUNqQjtJQUVBLE1BQU1JLG1CQUFtQjtRQUN2QkosZUFBZTtRQUNmRixrQkFBa0I7UUFDbEJJLGFBQWE7UUFDYkc7SUFDRjtJQUVBLE1BQU1BLGVBQWU7UUFDbkJILGFBQWE7SUFDZjtJQUVBLE1BQU1JLFVBQVU7UUFDZDtZQUNFQyxNQUFNakIsMEpBQU1BO1lBQ1prQixPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsU0FBUztRQUNYO1FBQ0E7WUFDRUgsTUFBTWhCLDBKQUFNQTtZQUNaaUIsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLFNBQVM7UUFDWDtLQUNEO0lBRUQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7O1lBQXlEO1lBQ3JFYiw2QkFDQyw4REFBQ2pCLDZFQUFlQTtnQkFDZCtCLFFBQVFkO2dCQUNSZSxlQUFlVjs7Ozs7OzBCQUduQiw4REFBQzdCLHlEQUFNQTtnQkFDTHdDLFNBQVNaO2dCQUNUUyxXQUFVOztvQkFDWDtvQkFBeUQ7a0NBRXhELDhEQUFDN0IsMkpBQVVBO3dCQUFDNkIsV0FBVTs7Ozs7O29CQUF1Qzs7Ozs7Ozs7Ozs7OztBQUlyRTtHQXBEU2hCO0tBQUFBO0FBc0RNLFNBQVNvQjs7O0lBQ3RCLE1BQU1DLFlBQVl2Qiw2Q0FBTUEsQ0FBb0I7SUFDNUMsTUFBTXdCLGVBQWV4Qiw2Q0FBTUE7SUFDM0IsTUFBTSxDQUFDeUIsU0FBU0MsV0FBVyxHQUFHekIsK0NBQVFBLENBQUM7SUFFdkMsaUNBQWlDO0lBQ2pDLE1BQU0wQixtQkFBbUIzQiw2Q0FBTUEsQ0FBQztRQUFFNEIsVUFBVTtRQUFHQyxPQUFPO1FBQUdDLFVBQVU7UUFBR0MsUUFBUTtJQUFFO0lBRWhGLG1DQUFtQztJQUNuQyxNQUFNQyxvQkFBb0I7UUFDeEJDLGNBQWM7UUFDZEMsV0FBVztRQUNYQyxPQUFPO1FBQ1BDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxXQUFXO1lBQUVDLEdBQUc7WUFBTUMsR0FBRztZQUFNQyxHQUFHO1FBQUs7UUFDdkNDLFlBQVk7WUFBRUgsR0FBRztZQUFLQyxHQUFHO1lBQUtDLEdBQUc7UUFBSTtRQUNyQ0UsaUJBQWlCO1lBQUVKLEdBQUc7WUFBS0MsR0FBRztZQUFLQyxHQUFHO1FBQUk7SUFDNUM7SUFFQSxvQ0FBb0M7SUFDcEMxQyxnREFBU0EsQ0FBQztRQUNSLE1BQU02QyxlQUFlO1lBQ25CLE1BQU1DLGNBQWNDLFlBQVlDLEdBQUc7WUFDbkMsTUFBTUMsV0FBV0MsT0FBT3hCLE9BQU87WUFDL0IsTUFBTXlCLFVBQVV2QixpQkFBaUJ3QixPQUFPO1lBRXhDLG1EQUFtRDtZQUNuRCxJQUFJRCxRQUFRcEIsUUFBUSxHQUFHLEdBQUc7Z0JBQ3hCLE1BQU1zQixZQUFZUCxjQUFjSyxRQUFRcEIsUUFBUTtnQkFDaEQsTUFBTXVCLFNBQVNMLFdBQVdFLFFBQVFyQixLQUFLO2dCQUV2QyxJQUFJdUIsWUFBWSxHQUFHO29CQUNqQiw4QkFBOEI7b0JBQzlCLE1BQU1FLGNBQWNELFNBQVNEO29CQUM3QkYsUUFBUXRCLFFBQVEsR0FBR3NCLFFBQVF0QixRQUFRLEdBQUcsTUFBTTBCLGNBQWMsSUFBSSxtQkFBbUI7O29CQUVqRix5Q0FBeUM7b0JBQ3pDLE1BQU1DLFlBQVksSUFBSSw2QkFBNkI7O29CQUNuREwsUUFBUW5CLE1BQU0sR0FBR3lCLEtBQUtDLEdBQUcsQ0FBQyxDQUFDRixXQUFXQyxLQUFLRSxHQUFHLENBQUNILFdBQVdMLFFBQVF0QixRQUFRLEdBQUc7Z0JBQy9FO1lBQ0Y7WUFFQXNCLFFBQVFwQixRQUFRLEdBQUdlO1lBQ25CSyxRQUFRckIsS0FBSyxHQUFHbUI7WUFDaEJ0QixXQUFXc0I7UUFDYjtRQUVBQyxPQUFPVSxnQkFBZ0IsQ0FBQyxVQUFVZixjQUFjO1lBQUVnQixTQUFTO1FBQUs7UUFDaEUsT0FBTyxJQUFNWCxPQUFPWSxtQkFBbUIsQ0FBQyxVQUFVakI7SUFDcEQsR0FBRyxFQUFFO0lBRUwsaUNBQWlDO0lBQ2pDN0MsZ0RBQVNBLENBQUM7UUFDUixNQUFNK0QsZ0JBQWdCQyxZQUFZO1lBQ2hDLE1BQU1iLFVBQVV2QixpQkFBaUJ3QixPQUFPO1lBQ3hDRCxRQUFRbkIsTUFBTSxJQUFJLEtBQUssb0JBQW9COztZQUMzQ21CLFFBQVF0QixRQUFRLElBQUk7UUFDdEIsR0FBRztRQUVILE9BQU8sSUFBTW9DLGNBQWNGO0lBQzdCLEdBQUcsRUFBRTtJQUVML0QsZ0RBQVNBLElBQUM7O1FBQ1IsTUFBTWtFLFNBQVMxQyxVQUFVNEIsT0FBTztRQUNoQyxJQUFJLENBQUNjLFFBQVE7UUFFYixNQUFNQyxNQUFNakIsT0FBT2tCLGdCQUFnQixJQUFJO1FBQ3ZDLE1BQU1DLFdBQVduQixPQUFPb0IsVUFBVTtRQUNsQyxNQUFNQyxZQUFZckIsT0FBT3NCLFdBQVc7UUFFcEMsa0lBQWtJO1FBQ2xJLE1BQU1DLFFBQVFKLFdBQVdGO1FBQ3pCLE1BQU1PLFNBQVNILFlBQVlKO1FBRTNCRCxPQUFPTyxLQUFLLEdBQUdBO1FBQ2ZQLE9BQU9RLE1BQU0sR0FBR0E7UUFFaEIsTUFBTUMsS0FBS1QsT0FBT1UsVUFBVSxDQUFDO1FBQzdCLElBQUksQ0FBQ0QsSUFBSTtZQUNQRSxRQUFRQyxLQUFLLENBQUM7WUFDZDtRQUNGO1FBRUFILEdBQUdJLFFBQVEsQ0FBQyxHQUFHLEdBQUdiLE9BQU9PLEtBQUssRUFBRVAsT0FBT1EsTUFBTSxHQUFHLHVCQUF1QjtRQUN2RUcsUUFBUUcsR0FBRyxDQUFDO1FBRVosTUFBTUMsUUFBUTtZQUFFQyxHQUFHO1lBQUdDLEdBQUc7UUFBRTtRQUMzQixNQUFNakQsZUFBZUQsa0JBQWtCQyxZQUFZO1FBQ25ELE1BQU1rRCxZQU1ELEVBQUU7UUFFUCxnRkFBZ0Y7UUFDaEYsTUFBTUMsZ0JBQWdCNUIsS0FBS0UsR0FBRyxDQUFDYyxPQUFPQztRQUN0QyxNQUFNWSxzQkFBc0JELGdCQUFnQixLQUFLLG9CQUFvQjtRQUNyRSxNQUFNRSx3QkFBd0JGLGdCQUFnQixNQUFNLG9CQUFvQjtRQUV4RSxrREFBa0Q7UUFDbEQsSUFBSyxJQUFJRyxJQUFJLEdBQUdBLElBQUl0RCxjQUFjc0QsSUFBSztZQUNyQyxNQUFNQyxTQUFTaEMsS0FBS2lDLE1BQU0sS0FBS0gsd0JBQXdCRDtZQUN2REYsVUFBVU8sSUFBSSxDQUFDO2dCQUNiVCxHQUFHekIsS0FBS2lDLE1BQU0sS0FBTWpCLENBQUFBLFFBQVEsSUFBSWdCLE1BQUssSUFBS0E7Z0JBQzFDTixHQUFHMUIsS0FBS2lDLE1BQU0sS0FBTWhCLENBQUFBLFNBQVMsSUFBSWUsTUFBSyxJQUFLQTtnQkFDM0NHLElBQUksQ0FBQ25DLEtBQUtpQyxNQUFNLEtBQUssR0FBRSxJQUFLekQsa0JBQWtCRyxLQUFLO2dCQUNuRHlELElBQUksQ0FBQ3BDLEtBQUtpQyxNQUFNLEtBQUssR0FBRSxJQUFLekQsa0JBQWtCRyxLQUFLO2dCQUNuREksR0FBR2lELFNBQVM7WUFDZDtRQUNGO1FBRUEsTUFBTUssa0JBQW1CO1FBT3pCLE1BQU1DLG9CQUFxQjtRQXVDM0IsNkJBQTZCO1FBQzdCLE1BQU1DLGdCQUFnQixDQUFDckIsSUFBMkJzQixjQUFzQkM7WUFDdEUsTUFBTUMsU0FBU3hCLEdBQUd5QixZQUFZLENBQUNGO1lBQy9CLElBQUksQ0FBQ0MsUUFBUSxNQUFNLElBQUlFLE1BQU07WUFFN0IxQixHQUFHc0IsWUFBWSxDQUFDRSxRQUFRRjtZQUN4QnRCLEdBQUdxQixhQUFhLENBQUNHO1lBRWpCLElBQUksQ0FBQ3hCLEdBQUcyQixrQkFBa0IsQ0FBQ0gsUUFBUXhCLEdBQUc0QixjQUFjLEdBQUc7Z0JBQ3JELE1BQU0sSUFBSUYsTUFBTSxpQ0FBaUMxQixHQUFHNkIsZ0JBQWdCLENBQUNMO1lBQ3ZFO1lBQ0EsT0FBT0E7UUFDVDtRQUVBLE1BQU1NLHFCQUFxQixDQUFDOUIsSUFBMkIrQixTQUF1QkM7WUFDNUUsTUFBTUMsa0JBQWtCakMsR0FBRzhCLGtCQUFrQixDQUFDQyxTQUFTQztZQUN2RCxJQUFJQyxvQkFBb0IsQ0FBQyxHQUFHO2dCQUMxQixNQUFNLElBQUlQLE1BQU0sMEJBQTBCTTtZQUM1QztZQUNBLE9BQU9DO1FBQ1Q7UUFFQSxNQUFNQyxvQkFBb0IsQ0FBQ2xDLElBQTJCK0IsU0FBdUJDO1lBQzNFLE1BQU1HLG9CQUFvQm5DLEdBQUdrQyxpQkFBaUIsQ0FBQ0gsU0FBU0M7WUFDeEQsSUFBSUcsc0JBQXNCLENBQUMsR0FBRztnQkFDNUIsTUFBTSxJQUFJVCxNQUFNLDRCQUE0Qk07WUFDOUM7WUFDQSxPQUFPRztRQUNUO1FBRUEsTUFBTUMsZUFBZWYsY0FBY3JCLElBQUltQixpQkFBaUJuQixHQUFHcUMsYUFBYTtRQUN4RSxNQUFNQyxpQkFBaUJqQixjQUFjckIsSUFBSW9CLG1CQUFtQnBCLEdBQUd1QyxlQUFlO1FBQzlFLE1BQU1SLFVBQVUvQixHQUFHd0MsYUFBYTtRQUNoQyxJQUFJLENBQUNULFNBQVMsTUFBTSxJQUFJTCxNQUFNO1FBRTlCMUIsR0FBR3lDLFlBQVksQ0FBQ1YsU0FBU0s7UUFDekJwQyxHQUFHeUMsWUFBWSxDQUFDVixTQUFTTztRQUN6QnRDLEdBQUcwQyxXQUFXLENBQUNYO1FBRWYsTUFBTVksYUFBYSxJQUFJQyxhQUFhO1lBQ2xDLENBQUM7WUFBSztZQUNOLENBQUM7WUFBSyxDQUFDO1lBQ1A7WUFBSztZQUNMO1lBQUssQ0FBQztTQUNQO1FBRUQsTUFBTUMsbUJBQW1CN0MsR0FBRzhDLFlBQVk7UUFDeEM5QyxHQUFHK0MsVUFBVSxDQUFDL0MsR0FBR2dELFlBQVksRUFBRUg7UUFDL0I3QyxHQUFHaUQsVUFBVSxDQUFDakQsR0FBR2dELFlBQVksRUFBRUwsWUFBWTNDLEdBQUdrRCxXQUFXO1FBRXpELE1BQU1DLGlCQUFpQmpCLGtCQUFrQmxDLElBQUkrQixTQUFTO1FBQ3REL0IsR0FBR29ELHVCQUF1QixDQUFDRDtRQUMzQm5ELEdBQUdxRCxtQkFBbUIsQ0FBQ0YsZ0JBQWdCLEdBQUduRCxHQUFHc0QsS0FBSyxFQUFFLE9BQU8sSUFBSSxHQUFHO1FBRWxFLE1BQU1DLGtCQUFrQnpCLG1CQUFtQjlCLElBQUkrQixTQUFTO1FBQ3hELE1BQU15QixxQkFBcUIxQixtQkFBbUI5QixJQUFJK0IsU0FBUztRQUMzRCxNQUFNMEIsY0FBYzNCLG1CQUFtQjlCLElBQUkrQixTQUFTO1FBQ3BELE1BQU0yQixlQUFlNUIsbUJBQW1COUIsSUFBSStCLFNBQVM7UUFDckQsTUFBTTRCLGtCQUFrQjdCLG1CQUFtQjlCLElBQUkrQixTQUFTO1FBQ3hELE1BQU02QixtQkFBbUI5QixtQkFBbUI5QixJQUFJK0IsU0FBUztRQUN6RCxNQUFNOEIsd0JBQXdCL0IsbUJBQW1COUIsSUFBSStCLFNBQVM7UUFFOUQsTUFBTStCLE9BQU87WUFDWCxtQ0FBbUM7WUFDbkMsTUFBTUMsZUFBZTlHLGlCQUFpQndCLE9BQU8sQ0FBQ3BCLE1BQU07WUFDcEQsTUFBTTJHLE9BQU9DLEtBQUs1RixHQUFHLEtBQUssTUFBTSxxQkFBcUI7O1lBRXJELDBFQUEwRTtZQUMxRSxJQUFLLElBQUl3QyxJQUFJLEdBQUdBLElBQUl0RCxjQUFjc0QsSUFBSztnQkFDckMsTUFBTXFELFdBQVd6RCxTQUFTLENBQUNJLEVBQUU7Z0JBRTdCLHlEQUF5RDtnQkFDekQsTUFBTXNELFlBQVk3RyxrQkFBa0JHLEtBQUssR0FBRyxNQUFNLG1EQUFtRDs7Z0JBQ3JHLE1BQU0yRyxnQkFBZ0J0RixLQUFLdUYsR0FBRyxDQUFDTCxPQUFPLE9BQU9uRCxJQUFJLE9BQU9zRCxZQUFZLElBQUksaUJBQWlCOztnQkFDekYsTUFBTUcsZ0JBQWdCeEYsS0FBS3lGLEdBQUcsQ0FBQ1AsT0FBTyxPQUFPbkQsSUFBSSxPQUFPc0QsWUFBWSxJQUFJLGlCQUFpQjs7Z0JBRXpGLDZEQUE2RDtnQkFDN0QsSUFBSUssUUFBUU4sU0FBU2pELEVBQUUsR0FBR21ELGdCQUFnQjtnQkFDMUMsSUFBSUssUUFBUVAsU0FBU2hELEVBQUUsR0FBR29ELGdCQUFnQjtnQkFFMUMsNkNBQTZDO2dCQUM3QyxJQUFJeEYsS0FBSzRGLEdBQUcsQ0FBQ1gsZ0JBQWdCLE1BQU07b0JBQ2pDLGtEQUFrRDtvQkFDbERVLFNBQVNWLGVBQWU7b0JBRXhCLHlEQUF5RDtvQkFDekQsTUFBTVksZUFBZTdGLEtBQUt1RixHQUFHLENBQUNMLE9BQU8sSUFBSW5ELElBQUksT0FBTy9CLEtBQUs0RixHQUFHLENBQUNYLGdCQUFnQixJQUFJLGlCQUFpQjs7b0JBQ2xHUyxTQUFTRztvQkFFVCwrQ0FBK0M7b0JBQy9DSCxTQUFTLENBQUMxRixLQUFLaUMsTUFBTSxLQUFLLEdBQUUsSUFBS2pDLEtBQUs0RixHQUFHLENBQUNYLGdCQUFnQjtvQkFDMURVLFNBQVMsQ0FBQzNGLEtBQUtpQyxNQUFNLEtBQUssR0FBRSxJQUFLakMsS0FBSzRGLEdBQUcsQ0FBQ1gsZ0JBQWdCO2dCQUM1RDtnQkFFQSxnREFBZ0Q7Z0JBQ2hELE1BQU1hLFdBQVc7Z0JBQ2pCLElBQUk5RixLQUFLNEYsR0FBRyxDQUFDRixTQUFTSSxVQUFVSixTQUFTLENBQUMxRixLQUFLaUMsTUFBTSxLQUFLLEdBQUUsSUFBSzZEO2dCQUNqRSxJQUFJOUYsS0FBSzRGLEdBQUcsQ0FBQ0QsU0FBU0csVUFBVUgsU0FBUyxDQUFDM0YsS0FBS2lDLE1BQU0sS0FBSyxHQUFFLElBQUs2RDtnQkFFakUsMkNBQTJDO2dCQUMzQ1YsU0FBUzNELENBQUMsSUFBSWlFO2dCQUNkTixTQUFTMUQsQ0FBQyxJQUFJaUU7Z0JBRWQseURBQXlEO2dCQUN6RFAsU0FBU2pELEVBQUUsR0FBR3VELFFBQVEsTUFBTSxvQ0FBb0M7O2dCQUNoRU4sU0FBU2hELEVBQUUsR0FBR3VELFFBQVE7Z0JBRXRCLHFCQUFxQjtnQkFDckIsSUFBSVAsU0FBUzNELENBQUMsR0FBRzJELFNBQVNyRyxDQUFDLElBQUlxRyxTQUFTM0QsQ0FBQyxHQUFHVCxRQUFRb0UsU0FBU3JHLENBQUMsRUFBRXFHLFNBQVNqRCxFQUFFLElBQUksQ0FBQztnQkFDaEYsSUFBSWlELFNBQVMxRCxDQUFDLEdBQUcwRCxTQUFTckcsQ0FBQyxJQUFJcUcsU0FBUzFELENBQUMsR0FBR1QsU0FBU21FLFNBQVNyRyxDQUFDLEVBQUVxRyxTQUFTaEQsRUFBRSxJQUFJLENBQUM7WUFDbkY7WUFFQSxNQUFNMkQsa0JBQWtCLElBQUlqQyxhQUFhLElBQUlyRjtZQUM3QyxJQUFLLElBQUlzRCxJQUFJLEdBQUdBLElBQUl0RCxjQUFjc0QsSUFBSztnQkFDckMsTUFBTWlFLFlBQVksSUFBSWpFO2dCQUN0QixNQUFNa0UsS0FBS3RFLFNBQVMsQ0FBQ0ksRUFBRTtnQkFDdkJnRSxlQUFlLENBQUNDLFlBQVksRUFBRSxHQUFHQyxHQUFHeEUsQ0FBQztnQkFDckNzRSxlQUFlLENBQUNDLFlBQVksRUFBRSxHQUFHQyxHQUFHdkUsQ0FBQztnQkFDckNxRSxlQUFlLENBQUNDLFlBQVksRUFBRSxHQUFHQyxHQUFHbEgsQ0FBQztZQUN2QztZQUVBbUMsR0FBR2dGLFVBQVUsQ0FBQ3pCLGlCQUFpQnNCO1lBQy9CN0UsR0FBR2lGLFNBQVMsQ0FBQ3pCLG9CQUFvQmpHLGVBQWUsMkJBQTJCO1lBQzNFeUMsR0FBR2tGLFNBQVMsQ0FBQ3pCLGFBQWEzRDtZQUMxQkUsR0FBR2tGLFNBQVMsQ0FBQ3hCLGNBQWMzRDtZQUMzQkMsR0FBR21GLFNBQVMsQ0FBQ3hCLGlCQUFpQnJHLGtCQUFrQk0sU0FBUyxDQUFDQyxDQUFDLEVBQUVQLGtCQUFrQk0sU0FBUyxDQUFDRSxDQUFDLEVBQUVSLGtCQUFrQk0sU0FBUyxDQUFDRyxDQUFDO1lBQ3pIaUMsR0FBR21GLFNBQVMsQ0FBQ3ZCLGtCQUFrQnRHLGtCQUFrQlUsVUFBVSxDQUFDSCxDQUFDLEVBQUVQLGtCQUFrQlUsVUFBVSxDQUFDRixDQUFDLEVBQUVSLGtCQUFrQlUsVUFBVSxDQUFDRCxDQUFDO1lBQzdIaUMsR0FBR21GLFNBQVMsQ0FBQ3RCLHVCQUF1QnZHLGtCQUFrQlcsZUFBZSxDQUFDSixDQUFDLEVBQUVQLGtCQUFrQlcsZUFBZSxDQUFDSCxDQUFDLEVBQUVSLGtCQUFrQlcsZUFBZSxDQUFDRixDQUFDO1lBQ2pKaUMsR0FBR29GLFVBQVUsQ0FBQ3BGLEdBQUdxRixjQUFjLEVBQUUsR0FBRztZQUVwQ3ZJLGFBQWEyQixPQUFPLEdBQUc2RyxzQkFBc0J4QjtRQUMvQztRQUVBLG9CQUFvQjtRQUNwQixNQUFNeUIsa0JBQWtCLENBQUNDO1lBQ3ZCLE1BQU1DLE9BQU9sRyxPQUFPbUcscUJBQXFCO1lBQ3pDcEYsTUFBTUMsQ0FBQyxHQUFHaUYsRUFBRUcsT0FBTyxHQUFHRixLQUFLRyxJQUFJO1lBQy9CdEYsTUFBTUUsQ0FBQyxHQUFHZ0YsRUFBRUssT0FBTyxHQUFHSixLQUFLSyxHQUFHO1FBQ2hDO1FBRUF2RyxPQUFPTixnQkFBZ0IsQ0FBQyxhQUFhc0c7UUFFckMsZ0JBQWdCO1FBQ2hCLE1BQU1RLGVBQWU7WUFDbkIsTUFBTUMsU0FBU3pILE9BQU9rQixnQkFBZ0IsSUFBSTtZQUMxQ0YsT0FBT08sS0FBSyxHQUFHdkIsT0FBT29CLFVBQVUsR0FBR3FHO1lBQ25DekcsT0FBT1EsTUFBTSxHQUFHeEIsT0FBT3NCLFdBQVcsR0FBR21HO1lBQ3JDaEcsR0FBR0ksUUFBUSxDQUFDLEdBQUcsR0FBR2IsT0FBT08sS0FBSyxFQUFFUCxPQUFPUSxNQUFNO1FBQy9DO1FBRUF4QixPQUFPVSxnQkFBZ0IsQ0FBQyxVQUFVOEc7UUFFbEMvRixHQUFHaUcsVUFBVSxDQUFDbEU7UUFDZCtCO1FBRUEsT0FBTztZQUNMLElBQUloSCxhQUFhMkIsT0FBTyxFQUFFO2dCQUN4QnlILHFCQUFxQnBKLGFBQWEyQixPQUFPO1lBQzNDO1lBQ0FjLE9BQU9KLG1CQUFtQixDQUFDLGFBQWFvRztZQUN4Q2hILE9BQU9ZLG1CQUFtQixDQUFDLFVBQVU0RztRQUN2QztJQUNGLDBDQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ3hKO2tEQUFjOzswQkFFYiw4REFBQ2dEO2dCQUNDNEcsS0FBS3RKO2dCQUVMdUosT0FBTztvQkFDTEMsUUFBUTtvQkFDUkMsV0FBVyxjQUE0QixPQUFkdkosVUFBVSxLQUFJLE9BQUssa0JBQWtCO2dCQUNoRTswREFKVTs7Ozs7OzBCQVVaLDhEQUFDd0o7MERBQWUsdUdBRWYsT0FEQ3hKLFVBQVUsS0FBSyxjQUFjOzBCQUU3Qiw0RUFBQ1I7OERBQWM7OEJBQ2IsNEVBQUNBO2tFQUFjOzswQ0FDYiw4REFBQ0E7MEVBQWM7O2tEQUNiLDhEQUFDQTtrRkFBYztrREFDYiw0RUFBQ2lLO3NGQUFlO3NEQUErQjs7Ozs7Ozs7Ozs7a0RBRWpELDhEQUFDQTtrRkFBZTtrREFBaUQ7Ozs7Ozs7Ozs7OzswQ0FHbkUsOERBQUNqSzswRUFBYzs7a0RBQ2IsOERBQUNuQixpREFBSUE7d0NBQUNxTCxNQUFLO3dDQUFJakssV0FBVTs7NENBQW1HOzBEQUUxSCw4REFBQ2dLOzBGQUFlOzs7Ozs7Ozs7Ozs7a0RBRWxCLDhEQUFDcEwsaURBQUlBO3dDQUFDcUwsTUFBSzt3Q0FBSWpLLFdBQVU7OzRDQUFtRzswREFFMUgsOERBQUNnSzswRkFBZTs7Ozs7Ozs7Ozs7O2tEQUVsQiw4REFBQ3BMLGlEQUFJQTt3Q0FBQ3FMLE1BQUs7d0NBQUlqSyxXQUFVOzs0Q0FBbUc7MERBRTFILDhEQUFDZ0s7MEZBQWU7Ozs7Ozs7Ozs7OztrREFFbEIsOERBQUNwTCxpREFBSUE7d0NBQUNxTCxNQUFLO3dDQUFJakssV0FBVTs7NENBQW1HOzBEQUUxSCw4REFBQ2dLOzBGQUFlOzs7Ozs7Ozs7Ozs7a0RBR2xCLDhEQUFDck0seURBQU1BO3dDQUFDcUMsV0FBVTs7MERBQ2hCLDhEQUFDNUIsMkpBQU1BO2dEQUFDNEIsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUzdDLDhEQUFDa0s7MERBQWtCOzBCQUNqQiw0RUFBQ25LOzhEQUFjOztzQ0FFYiw4REFBQ29LO3NFQUFhOztnQ0FBb0Q7Z0NBQzdDOzhDQUNuQiw4REFBQ0g7b0NBRUNKLE9BQU87d0NBQ0xRLFFBQVE7d0NBQ1JDLFdBQVc7b0NBQ2I7OEVBSlU7OENBS1g7Ozs7Ozs7Ozs7OztzQ0FNSCw4REFBQ0M7c0VBQVk7c0NBQXdEOzs7Ozs7c0NBTXJFLDhEQUFDdks7c0VBQWM7c0NBQ2IsNEVBQUNmOzs7Ozs7Ozs7O3NDQU9ILDhEQUFDZTtzRUFBYzs7Z0NBQWlFOzhDQUM5RSw4REFBQ25CLGlEQUFJQTtvQ0FBQ3FMLE1BQUs7b0NBQUlqSyxXQUFVOzt3Q0FBZ0k7c0RBQ3ZKLDhEQUFDM0IsMkpBQU9BOzRDQUFDMkIsV0FBVTs7Ozs7O3dDQUEwQjtzREFDN0MsOERBQUNnSzs7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFUiw4REFBQ3BMLGlEQUFJQTtvQ0FBQ3FMLE1BQUs7b0NBQUlqSyxXQUFVOztzREFDdkIsOERBQUMxQiwySkFBYUE7NENBQUMwQixXQUFVOzs7Ozs7c0RBQ3pCLDhEQUFDZ0s7O3NEQUFLOzs7Ozs7Ozs7Ozs7OENBRVIsOERBQUNwTCxpREFBSUE7b0NBQUNxTCxNQUFLO29DQUFJakssV0FBVTs7c0RBQ3ZCLDhEQUFDekIsMkpBQVFBOzRDQUFDeUIsV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ2dLOztzREFBSzs7Ozs7Ozs7Ozs7OzhDQUVSLDhEQUFDcEwsaURBQUlBO29DQUFDcUwsTUFBSztvQ0FBSWpLLFdBQVU7O3NEQUN2Qiw4REFBQ3hCLDJKQUFJQTs0Q0FBQ3dCLFdBQVU7Ozs7OztzREFDaEIsOERBQUNnSzs7c0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9kLDhEQUFDaks7Z0JBQW9DNkosT0FBTztvQkFDMUNXLFlBQVk7b0JBQ1pDLGdCQUFnQjtnQkFDbEI7MERBSGU7O2tDQUliLDhEQUFDeks7a0VBQWM7OzBDQUNiLDhEQUFDQTswRUFBYzs7a0RBQ2IsOERBQUMwSztrRkFBYTtrREFBNkM7Ozs7OztrREFDM0QsOERBQUNIO2tGQUFZO2tEQUEwRDs7Ozs7Ozs7Ozs7OzBDQUt6RSw4REFBQ3ZLOzBFQUFjOztrREFFYiw4REFBQ2xDLHFEQUFJQTt3Q0FBQ21DLFdBQVU7OzBEQUNkLDhEQUFDaEMsMkRBQVVBO2dEQUFDZ0MsV0FBVTs7a0VBQ3BCLDhEQUFDRDtrR0FBYztrRUFDYiw0RUFBQ3JCLDBKQUFNQTs0REFBQ3NCLFdBQVU7Ozs7Ozs7Ozs7O2tFQUVwQiw4REFBQy9CLDBEQUFTQTt3REFBQytCLFdBQVU7a0VBQXdEOzs7Ozs7Ozs7Ozs7MERBRS9FLDhEQUFDbEMsNERBQVdBO2dEQUFDa0MsV0FBVTswREFDckIsNEVBQUMwSzs4RkFBYTs7c0VBQ1osOERBQUNDOztzRUFBRzs7Ozs7O3NFQUNKLDhEQUFDQTs7c0VBQUc7Ozs7OztzRUFDSiw4REFBQ0E7O3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBOztzRUFBRzs7Ozs7O3NFQUNKLDhEQUFDQTs7c0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU1WLDhEQUFDOU0scURBQUlBO3dDQUFDbUMsV0FBVTs7MERBQ2QsOERBQUNoQywyREFBVUE7Z0RBQUNnQyxXQUFVOztrRUFDcEIsOERBQUNEO2tHQUFjO2tFQUNiLDRFQUFDcEIsMEpBQU1BOzREQUFDcUIsV0FBVTs7Ozs7Ozs7Ozs7a0VBRXBCLDhEQUFDL0IsMERBQVNBO3dEQUFDK0IsV0FBVTtrRUFBd0Q7Ozs7Ozs7Ozs7OzswREFFL0UsOERBQUNsQyw0REFBV0E7Z0RBQUNrQyxXQUFVOzBEQUNyQiw0RUFBQzBLOzhGQUFhOztzRUFDWiw4REFBQ0M7O3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBOztzRUFBRzs7Ozs7O3NFQUNKLDhEQUFDQTs7c0VBQUc7Ozs7OztzRUFDSiw4REFBQ0E7O3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBOztzRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUWQsOERBQUM1SztrRUFBYztrQ0FDYiw0RUFBQ0E7c0VBQWM7OzhDQUNiLDhEQUFDQTs4RUFBYzs7c0RBQ2IsOERBQUMwSztzRkFBYTtzREFBd0M7Ozs7OztzREFDdEQsOERBQUNIO3NGQUFZO3NEQUFrRDs7Ozs7Ozs7Ozs7OzhDQUtqRSw4REFBQ3ZLOzhFQUFjOztzREFFYiw4REFBQ2xDLHFEQUFJQTs0Q0FBQ21DLFdBQVU7OzhEQUNkLDhEQUFDaEMsMkRBQVVBO29EQUFDZ0MsV0FBVTs7c0VBQ3BCLDhEQUFDRDtzR0FBYztzRUFDYiw0RUFBQ3JCLDBKQUFNQTtnRUFBQ3NCLFdBQVU7Ozs7Ozs7Ozs7O3NFQUVwQiw4REFBQy9CLDBEQUFTQTs0REFBQytCLFdBQVU7c0VBQXdDOzs7Ozs7Ozs7Ozs7OERBRS9ELDhEQUFDbEMsNERBQVdBO29EQUFDa0MsV0FBVTs7c0VBQ3JCLDhEQUFDakMsZ0VBQWVBOzREQUFDaUMsV0FBVTtzRUFBNkM7Ozs7OztzRUFHeEUsOERBQUNyQyx5REFBTUE7NERBQUNpTixTQUFROzREQUFRNUssV0FBVTtzRUFBa0c7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFPeEksOERBQUNuQyxxREFBSUE7NENBQUNtQyxXQUFVOzs4REFDZCw4REFBQ2hDLDJEQUFVQTtvREFBQ2dDLFdBQVU7O3NFQUNwQiw4REFBQ0Q7c0dBQWM7c0VBQ2IsNEVBQUNwQiwwSkFBTUE7Z0VBQUNxQixXQUFVOzs7Ozs7Ozs7OztzRUFFcEIsOERBQUMvQiwwREFBU0E7NERBQUMrQixXQUFVO3NFQUF3Qzs7Ozs7Ozs7Ozs7OzhEQUUvRCw4REFBQ2xDLDREQUFXQTtvREFBQ2tDLFdBQVU7O3NFQUNyQiw4REFBQ2pDLGdFQUFlQTs0REFBQ2lDLFdBQVU7c0VBQTZDOzs7Ozs7c0VBR3hFLDhEQUFDckMseURBQU1BOzREQUFDaU4sU0FBUTs0REFBUTVLLFdBQVU7c0VBQXdHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTzlJLDhEQUFDbkMscURBQUlBOzRDQUFDbUMsV0FBVTs7OERBQ2QsOERBQUNoQywyREFBVUE7b0RBQUNnQyxXQUFVOztzRUFDcEIsOERBQUNEO3NHQUFjO3NFQUNiLDRFQUFDdEIsMkpBQVVBO2dFQUFDdUIsV0FBVTs7Ozs7Ozs7Ozs7c0VBRXhCLDhEQUFDL0IsMERBQVNBOzREQUFDK0IsV0FBVTtzRUFBd0M7Ozs7Ozs7Ozs7Ozs4REFFL0QsOERBQUNsQyw0REFBV0E7b0RBQUNrQyxXQUFVOztzRUFDckIsOERBQUNqQyxnRUFBZUE7NERBQUNpQyxXQUFVO3NFQUE2Qzs7Ozs7O3NFQUd4RSw4REFBQ3JDLHlEQUFNQTs0REFBQ2lOLFNBQVE7NERBQVE1SyxXQUFVO3NFQUFxRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBVWpKLDhEQUFDRDtrRUFBYzs7MENBQ2IsOERBQUNBOzBFQUFjOzs7Ozs7MENBQ2YsOERBQUNBOzBFQUFjOzBDQUNiLDRFQUFDbEMscURBQUlBO29DQUFDbUMsV0FBVTs7c0RBQ2QsOERBQUNoQywyREFBVUE7NENBQUNnQyxXQUFVOzs4REFDcEIsOERBQUNEOzhGQUFjOzhEQUNiLDRFQUFDckIsMEpBQU1BO3dEQUFDc0IsV0FBVTs7Ozs7Ozs7Ozs7OERBRXBCLDhEQUFDL0IsMERBQVNBO29EQUFDK0IsV0FBVTs4REFBd0M7Ozs7Ozs7Ozs7OztzREFFL0QsOERBQUNsQyw0REFBV0E7NENBQUNrQyxXQUFVO3NEQUNyQiw0RUFBQ2pDLGdFQUFlQTtnREFBQ2lDLFdBQVU7MERBQTZDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU05RSw4REFBQ0Q7MEVBQWM7O2tEQUViLDhEQUFDQTtrRkFBYzs7MERBQ2IsOERBQUNwQiwwSkFBTUE7Z0RBQUNxQixXQUFVOzs7Ozs7MERBQ2xCLDhEQUFDNks7MEZBQWE7MERBQXdDOzs7Ozs7MERBQ3RELDhEQUFDUDswRkFBWTswREFBNkI7Ozs7Ozs7Ozs7OztrREFFNUMsOERBQUNHO2tGQUFhO2tEQUF3Qzs7Ozs7O2tEQUN0RCw4REFBQ0g7a0ZBQVk7a0RBQWdFOzs7Ozs7a0RBSzdFLDhEQUFDdks7a0ZBQWM7OzRDQUFzQzswREFDbkQsOERBQUNBOzBGQUFjOztvREFBa0M7a0VBQy9DLDhEQUFDbkMsdURBQUtBO3dEQUNKa04sSUFBRzt3REFDSEMsYUFBWTt3REFDWi9LLFdBQVU7Ozs7OztrRUFFWiw4REFBQ3JDLHlEQUFNQTt3REFDTHFDLFdBQVUsMkdBQTJHLG1DQUFtQzs7d0RBQ3hKRyxTQUFTOzREQUNQLE1BQU02SyxhQUFhQyxTQUFTQyxjQUFjLENBQUM7NERBQzNDLE1BQU1DLFFBQVFILFdBQVdJLEtBQUs7NERBQzlCLE1BQU1DLGFBQWE7NERBRW5CLElBQUlBLFdBQVdDLElBQUksQ0FBQ0gsUUFBUTtnRUFDMUIsTUFBTUksU0FBU1AsV0FBV1Esa0JBQWtCO2dFQUM1Q0QsT0FBT0UsV0FBVyxHQUFHO2dFQUNyQkYsT0FBT0csU0FBUyxDQUFDQyxNQUFNLENBQUMsZUFBZTtnRUFDdkNKLE9BQU9HLFNBQVMsQ0FBQ0UsR0FBRyxDQUFDLGdCQUFnQjs0REFDdkMsT0FBTztnRUFDTEMsTUFBTTs0REFDUjt3REFDRjtrRUFDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVNULDhEQUFDQztrRUFBaUI7a0NBQ2hCLDRFQUFDL0w7c0VBQWM7c0NBQ2IsNEVBQUNBOzBFQUFjOztrREFFYiw4REFBQ0E7a0ZBQWM7OzBEQUNiLDhEQUFDQTswRkFBYzswREFDYiw0RUFBQ2dNO29EQUNDQyxLQUFJO29EQUNKQyxLQUFJO29EQUVKckMsT0FBTzt3REFBRVEsUUFBUTtvREFBMEI7OEZBRGpDOzs7Ozs7Ozs7OzswREFJZCw4REFBQ0o7MEZBQWU7MERBQWdDOzs7Ozs7Ozs7Ozs7a0RBSWxELDhEQUFDaks7a0ZBQWM7OzBEQUNiLDhEQUFDbkIsaURBQUlBO2dEQUFDcUwsTUFBSztnREFBSWpLLFdBQVU7O2tFQUN2Qiw4REFBQzNCLDJKQUFPQTt3REFBQzJCLFdBQVU7Ozs7OztrRUFDbkIsOERBQUNnSztrR0FBZTtrRUFBVTs7Ozs7Ozs7Ozs7OzBEQUU1Qiw4REFBQ3BMLGlEQUFJQTtnREFBQ3FMLE1BQUs7Z0RBQUlqSyxXQUFVOztrRUFDdkIsOERBQUMxQiwySkFBYUE7d0RBQUMwQixXQUFVOzs7Ozs7a0VBQ3pCLDhEQUFDZ0s7a0dBQWU7a0VBQVU7Ozs7Ozs7Ozs7OzswREFFNUIsOERBQUNwTCxpREFBSUE7Z0RBQUNxTCxNQUFLO2dEQUFJakssV0FBVTs7a0VBQ3ZCLDhEQUFDekIsMkpBQVFBO3dEQUFDeUIsV0FBVTs7Ozs7O2tFQUNwQiw4REFBQ2dLO2tHQUFlO2tFQUFVOzs7Ozs7Ozs7Ozs7MERBRTVCLDhEQUFDcEwsaURBQUlBO2dEQUFDcUwsTUFBSztnREFBSWpLLFdBQVU7O2tFQUN2Qiw4REFBQ3hCLDJKQUFJQTt3REFBQ3dCLFdBQVU7Ozs7OztrRUFDaEIsOERBQUNnSztrR0FBZTtrRUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUs5Qiw4REFBQ2pLO2tGQUFjO2tEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFzQ3JEO0lBbnJCd0JLO01BQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9wYWdlLnRzeD83NjAzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiXHJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXHJcbmltcG9ydCB7IENvaG9ydEluZm9Nb2RhbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY29ob3J0LWluZm8tbW9kYWxcIlxyXG5pbXBvcnQge1xyXG4gIEFycm93UmlnaHQsXHJcbiAgV2FsbGV0LFxyXG4gIFR3aXR0ZXIsXHJcbiAgTWVzc2FnZUNpcmNsZSxcclxuICBGaWxlVGV4dCxcclxuICBTZW5kLFxyXG4gIERvbGxhclNpZ24sXHJcbiAgUm9ja2V0LFxyXG4gIFNoaWVsZCxcclxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiXHJcblxyXG5mdW5jdGlvbiBKb2luQ29ob3J0U3BsaXRCdXR0b24oKSB7XHJcbiAgY29uc3QgW3NlbGVjdGVkT3B0aW9uLCBzZXRTZWxlY3RlZE9wdGlvbl0gPSB1c2VTdGF0ZTxcIlByb2plY3RcIiB8IFwiQ3VyYXRvclwiIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2lzTW9kYWxPcGVuLCBzZXRJc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Jlc2V0Rm9ybSwgc2V0UmVzZXRGb3JtXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU3BsaXQgPSAoKSA9PiB7XHJcbiAgICBzZXRJc01vZGFsT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbG9zZU1vZGFsID0gKCkgPT4ge1xyXG4gICAgc2V0SXNNb2RhbE9wZW4oZmFsc2UpO1xyXG4gICAgc2V0U2VsZWN0ZWRPcHRpb24obnVsbCk7XHJcbiAgICBzZXRSZXNldEZvcm0odHJ1ZSk7XHJcbiAgICByZXNldFRoZUZvcm0oKTtcclxuICB9O1xyXG5cclxuICBjb25zdCByZXNldFRoZUZvcm0gPSAoKSA9PiB7XHJcbiAgICBzZXRSZXNldEZvcm0oZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGJ1dHRvbnMgPSBbXHJcbiAgICB7XHJcbiAgICAgIGljb246IFJvY2tldCxcclxuICAgICAgbGFiZWw6IFwiUHJvamVjdFwiLFxyXG4gICAgICBhY3Rpb246IFwiUHJvamVjdFwiLFxyXG4gICAgICB0b29sdGlwOiBcIkpvaW4gYXMgYSBwcm9qZWN0IC0gR2V0IGZlZWRiYWNrLCBlY29zeXN0ZW0gaW50cm9zLCBhbmQgbWlsZXN0b25lIHJld2FyZHNcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGljb246IFNoaWVsZCxcclxuICAgICAgbGFiZWw6IFwiQ3VyYXRvclwiLFxyXG4gICAgICBhY3Rpb246IFwiQ3VyYXRvclwiLFxyXG4gICAgICB0b29sdGlwOiBcIkpvaW4gYXMgYSBjdXJhdG9yIC0gR2V0IHByb2plY3QgaW50cm9zLCBtb25ldGl6YXRpb24gb3Bwb3J0dW5pdGllcywgYW5kIHJld2FyZHNcIixcclxuICAgIH0sXHJcbiAgXTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC0yMCBteC1hdXRvXCI+IHsvKiBSZW1vdmVkIHctZnVsbCAqL31cclxuICAgICAge2lzTW9kYWxPcGVuICYmIChcclxuICAgICAgICA8Q29ob3J0SW5mb01vZGFsXHJcbiAgICAgICAgICBpc09wZW49e2lzTW9kYWxPcGVufVxyXG4gICAgICAgICAgb25DbG9zZUFjdGlvbj17aGFuZGxlQ2xvc2VNb2RhbH1cclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG4gICAgICA8QnV0dG9uXHJcbiAgICAgICAgb25DbGljaz17aGFuZGxlU3BsaXR9XHJcbiAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWJsdWUtNzAwIGhvdmVyOmZyb20tYmx1ZS03MDAgaG92ZXI6dG8tYmx1ZS04MDAgdGV4dC13aGl0ZSBweC04IHB5LTQgdGV4dC1sZyBtZDpweC0xMiBtZDpweS01IG1kOnRleHQteGwgZm9udC1ib2xkIHJvdW5kZWQteGwgc2hhZG93LXhsIGhvdmVyOnNoYWRvdy0yeGwgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwIG1heC13LXhzIHNtOm1heC13LXNtXCJcclxuICAgICAgPiB7LyogQWRkZWQgcmVzcG9uc2l2ZSBwYWRkaW5nL3RleHQgc2l6ZSBhbmQgbWF4LXdpZHRoICovfVxyXG4gICAgICAgIEpvaW4gVGhlIEZpcnN0IENvaG9ydFxyXG4gICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cInctNSBoLTUgbWwtMiBtZDp3LTYgbWQ6aC02IG1kOm1sLTNcIiAvPiB7LyogQWRqdXN0ZWQgaWNvbiBzaXplICovfVxyXG4gICAgICA8L0J1dHRvbj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWVQYWdlKCkge1xyXG4gIGNvbnN0IGNhbnZhc1JlZiA9IHVzZVJlZjxIVE1MQ2FudmFzRWxlbWVudD4obnVsbClcclxuICBjb25zdCBhbmltYXRpb25SZWYgPSB1c2VSZWY8bnVtYmVyPigpXHJcbiAgY29uc3QgW3Njcm9sbFksIHNldFNjcm9sbFldID0gdXNlU3RhdGUoMClcclxuXHJcbiAgLy8gU3VidGxlIHNjcm9sbCBwaHlzaWNzIHRyYWNraW5nXHJcbiAgY29uc3Qgc2Nyb2xsUGh5c2ljc1JlZiA9IHVzZVJlZih7IHZlbG9jaXR5OiAwLCBsYXN0WTogMCwgbGFzdFRpbWU6IDAsIGVmZmVjdDogMCB9KVxyXG5cclxuICAvLyBGaXhlZCBvcHRpbWFsIGFuaW1hdGlvbiBzZXR0aW5nc1xyXG4gIGNvbnN0IGFuaW1hdGlvblNldHRpbmdzID0ge1xyXG4gICAgbnVtTWV0YWJhbGxzOiAyMSxcclxuICAgIGVkZ2VXaWR0aDogMC4wMixcclxuICAgIHNwZWVkOiAyLjEsXHJcbiAgICB0aHJlc2hvbGQ6IDEuMTQsXHJcbiAgICBpbnRlbnNpdHk6IDEyLjUsXHJcbiAgICBibHVlQ29sb3I6IHsgcjogMC4yMywgZzogMC41MSwgYjogMC45NiB9LFxyXG4gICAgd2hpdGVDb2xvcjogeyByOiAxLjAsIGc6IDEuMCwgYjogMS4wIH0sXHJcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IHsgcjogMS4wLCBnOiAxLjAsIGI6IDEuMCB9XHJcbiAgfVxyXG5cclxuICAvLyBIYW5kbGUgc2Nyb2xsIHdpdGggc3VidGxlIHBoeXNpY3NcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlU2Nyb2xsID0gKCkgPT4ge1xyXG4gICAgICBjb25zdCBjdXJyZW50VGltZSA9IHBlcmZvcm1hbmNlLm5vdygpXHJcbiAgICAgIGNvbnN0IGN1cnJlbnRZID0gd2luZG93LnNjcm9sbFlcclxuICAgICAgY29uc3QgcGh5c2ljcyA9IHNjcm9sbFBoeXNpY3NSZWYuY3VycmVudFxyXG5cclxuICAgICAgLy8gQ2FsY3VsYXRlIHZlbG9jaXR5IG9ubHkgaWYgd2UgaGF2ZSBwcmV2aW91cyBkYXRhXHJcbiAgICAgIGlmIChwaHlzaWNzLmxhc3RUaW1lID4gMCkge1xyXG4gICAgICAgIGNvbnN0IGRlbHRhVGltZSA9IGN1cnJlbnRUaW1lIC0gcGh5c2ljcy5sYXN0VGltZVxyXG4gICAgICAgIGNvbnN0IGRlbHRhWSA9IGN1cnJlbnRZIC0gcGh5c2ljcy5sYXN0WVxyXG5cclxuICAgICAgICBpZiAoZGVsdGFUaW1lID4gMCkge1xyXG4gICAgICAgICAgLy8gU21vb3RoIHZlbG9jaXR5IGNhbGN1bGF0aW9uXHJcbiAgICAgICAgICBjb25zdCBuZXdWZWxvY2l0eSA9IGRlbHRhWSAvIGRlbHRhVGltZVxyXG4gICAgICAgICAgcGh5c2ljcy52ZWxvY2l0eSA9IHBoeXNpY3MudmVsb2NpdHkgKiAwLjcgKyBuZXdWZWxvY2l0eSAqIDAuMyAvLyBTbW9vdGggYXZlcmFnaW5nXHJcblxyXG4gICAgICAgICAgLy8gQ3JlYXRlIHN1YnRsZSBlZmZlY3QgYmFzZWQgb24gdmVsb2NpdHlcclxuICAgICAgICAgIGNvbnN0IG1heEVmZmVjdCA9IDAuMiAvLyBWZXJ5IHN1YnRsZSBtYXhpbXVtIGVmZmVjdFxyXG4gICAgICAgICAgcGh5c2ljcy5lZmZlY3QgPSBNYXRoLm1heCgtbWF4RWZmZWN0LCBNYXRoLm1pbihtYXhFZmZlY3QsIHBoeXNpY3MudmVsb2NpdHkgKiAwLjEpKVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgcGh5c2ljcy5sYXN0VGltZSA9IGN1cnJlbnRUaW1lXHJcbiAgICAgIHBoeXNpY3MubGFzdFkgPSBjdXJyZW50WVxyXG4gICAgICBzZXRTY3JvbGxZKGN1cnJlbnRZKVxyXG4gICAgfVxyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwsIHsgcGFzc2l2ZTogdHJ1ZSB9KVxyXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpXHJcbiAgfSwgW10pXHJcblxyXG4gIC8vIEdlbnRsZSBkZWNheSBvZiBzY3JvbGwgZWZmZWN0c1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBkZWNheUludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICBjb25zdCBwaHlzaWNzID0gc2Nyb2xsUGh5c2ljc1JlZi5jdXJyZW50XHJcbiAgICAgIHBoeXNpY3MuZWZmZWN0ICo9IDAuODggLy8gVmVyeSBnZW50bGUgZGVjYXlcclxuICAgICAgcGh5c2ljcy52ZWxvY2l0eSAqPSAwLjk1XHJcbiAgICB9LCAxNilcclxuXHJcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChkZWNheUludGVydmFsKVxyXG4gIH0sIFtdKVxyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgY2FudmFzID0gY2FudmFzUmVmLmN1cnJlbnRcclxuICAgIGlmICghY2FudmFzKSByZXR1cm5cclxuXHJcbiAgICBjb25zdCBkcHIgPSB3aW5kb3cuZGV2aWNlUGl4ZWxSYXRpbyB8fCAxO1xyXG4gICAgY29uc3QgY3NzV2lkdGggPSB3aW5kb3cuaW5uZXJXaWR0aDtcclxuICAgIGNvbnN0IGNzc0hlaWdodCA9IHdpbmRvdy5pbm5lckhlaWdodDtcclxuXHJcbiAgICAvLyBVc2UgZGV2aWNlLXBpeGVs4oCTc2NhbGVkIGRpbWVuc2lvbnMgZm9yIGFsbCBXZWJHTCBtYXRocyBzbyB0aGUgZWZmZWN0IGZpbGxzIHRoZSBlbnRpcmUgc2NyZWVuIG9uIGhpZ2gtRFBSIChlLmcuIG1vYmlsZSkgZGlzcGxheXNcclxuICAgIGNvbnN0IHdpZHRoID0gY3NzV2lkdGggKiBkcHI7XHJcbiAgICBjb25zdCBoZWlnaHQgPSBjc3NIZWlnaHQgKiBkcHI7XHJcblxyXG4gICAgY2FudmFzLndpZHRoID0gd2lkdGg7XHJcbiAgICBjYW52YXMuaGVpZ2h0ID0gaGVpZ2h0O1xyXG5cclxuICAgIGNvbnN0IGdsID0gY2FudmFzLmdldENvbnRleHQoXCJ3ZWJnbFwiKVxyXG4gICAgaWYgKCFnbCkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiV2ViR0wgbm90IHN1cHBvcnRlZFwiKVxyXG4gICAgICByZXR1cm5cclxuICAgIH1cclxuXHJcbiAgICBnbC52aWV3cG9ydCgwLCAwLCBjYW52YXMud2lkdGgsIGNhbnZhcy5oZWlnaHQpOyAvLyBTZXQgdmlld3BvcnQgZm9yIERQUlxyXG4gICAgY29uc29sZS5sb2coXCJXZWJHTCBjb250ZXh0IGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5XCIpO1xyXG5cclxuICAgIGNvbnN0IG1vdXNlID0geyB4OiAwLCB5OiAwIH1cclxuICAgIGNvbnN0IG51bU1ldGFiYWxscyA9IGFuaW1hdGlvblNldHRpbmdzLm51bU1ldGFiYWxsc1xyXG4gICAgY29uc3QgbWV0YWJhbGxzOiBBcnJheTx7XHJcbiAgICAgIHg6IG51bWJlclxyXG4gICAgICB5OiBudW1iZXJcclxuICAgICAgdng6IG51bWJlclxyXG4gICAgICB2eTogbnVtYmVyXHJcbiAgICAgIHI6IG51bWJlclxyXG4gICAgfT4gPSBbXVxyXG5cclxuICAgIC8vIENhbGN1bGF0ZSByZXNwb25zaXZlIHJhZGlpIGJhc2VkIG9uIHRoZSBzbWFsbGVyIGRpbWVuc2lvbiwgbWFraW5nIHRoZW0gbGFyZ2VyXHJcbiAgICBjb25zdCBiYXNlRGltZW5zaW9uID0gTWF0aC5taW4od2lkdGgsIGhlaWdodCk7XHJcbiAgICBjb25zdCByZXNwb25zaXZlTWluUmFkaXVzID0gYmFzZURpbWVuc2lvbiAqIDAuMTsgLy8gRG91YmxlZCBmcm9tIDAuMDVcclxuICAgIGNvbnN0IHJlc3BvbnNpdmVSYWRpdXNSYW5nZSA9IGJhc2VEaW1lbnNpb24gKiAwLjA4OyAvLyBEb3VibGVkIGZyb20gMC4wNFxyXG5cclxuICAgIC8vIEluaXRpYWxpemUgbWV0YWJhbGxzIHdpdGggY29uZmlndXJhYmxlIHNldHRpbmdzXHJcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IG51bU1ldGFiYWxsczsgaSsrKSB7XHJcbiAgICAgIGNvbnN0IHJhZGl1cyA9IE1hdGgucmFuZG9tKCkgKiByZXNwb25zaXZlUmFkaXVzUmFuZ2UgKyByZXNwb25zaXZlTWluUmFkaXVzO1xyXG4gICAgICBtZXRhYmFsbHMucHVzaCh7XHJcbiAgICAgICAgeDogTWF0aC5yYW5kb20oKSAqICh3aWR0aCAtIDIgKiByYWRpdXMpICsgcmFkaXVzLFxyXG4gICAgICAgIHk6IE1hdGgucmFuZG9tKCkgKiAoaGVpZ2h0IC0gMiAqIHJhZGl1cykgKyByYWRpdXMsXHJcbiAgICAgICAgdng6IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIGFuaW1hdGlvblNldHRpbmdzLnNwZWVkLFxyXG4gICAgICAgIHZ5OiAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiBhbmltYXRpb25TZXR0aW5ncy5zcGVlZCxcclxuICAgICAgICByOiByYWRpdXMgKiAwLjc1LFxyXG4gICAgICB9KVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHZlcnRleFNoYWRlclNyYyA9IGBcclxuICAgICAgYXR0cmlidXRlIHZlYzIgcG9zaXRpb247XHJcbiAgICAgIHZvaWQgbWFpbigpIHtcclxuICAgICAgICBnbF9Qb3NpdGlvbiA9IHZlYzQocG9zaXRpb24sIDAuMCwgMS4wKTtcclxuICAgICAgfVxyXG4gICAgYFxyXG5cclxuICAgIGNvbnN0IGZyYWdtZW50U2hhZGVyU3JjID0gYFxyXG4gICAgICBwcmVjaXNpb24gaGlnaHAgZmxvYXQ7XHJcbiAgICAgIHVuaWZvcm0gZmxvYXQgdV93aWR0aDtcclxuICAgICAgdW5pZm9ybSBmbG9hdCB1X2hlaWdodDtcclxuICAgICAgdW5pZm9ybSBpbnQgdV9udW1NZXRhYmFsbHM7XHJcbiAgICAgIHVuaWZvcm0gdmVjMyBtZXRhYmFsbHNbMjFdO1xyXG4gICAgICB1bmlmb3JtIHZlYzMgdV9ibHVlQ29sb3I7XHJcbiAgICAgIHVuaWZvcm0gdmVjMyB1X3doaXRlQ29sb3I7XHJcbiAgICAgIHVuaWZvcm0gdmVjMyB1X2JhY2tncm91bmRDb2xvcjtcclxuXHJcbiAgICAgIHZvaWQgbWFpbigpe1xyXG4gICAgICAgIGZsb2F0IHggPSBnbF9GcmFnQ29vcmQueDtcclxuICAgICAgICBmbG9hdCB5ID0gZ2xfRnJhZ0Nvb3JkLnk7XHJcbiAgICAgICAgZmxvYXQgc3VtID0gMC4wO1xyXG5cclxuICAgICAgICBmb3IgKGludCBpID0gMDsgaSA8IDIxOyBpKyspIHtcclxuICAgICAgICAgIGlmIChpID49IHVfbnVtTWV0YWJhbGxzKSBicmVhaztcclxuICAgICAgICAgIHZlYzMgbWV0YWJhbGwgPSBtZXRhYmFsbHNbaV07XHJcbiAgICAgICAgICBmbG9hdCBkeCA9IG1ldGFiYWxsLnggLSB4O1xyXG4gICAgICAgICAgZmxvYXQgZHkgPSBtZXRhYmFsbC55IC0geTtcclxuICAgICAgICAgIGZsb2F0IHJhZGl1cyA9IG1ldGFiYWxsLno7XHJcbiAgICAgICAgICBzdW0gKz0gKHJhZGl1cyAqIHJhZGl1cykgLyAoZHggKiBkeCArIGR5ICogZHkpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZmxvYXQgdGhyZXNob2xkID0gMC41O1xyXG4gICAgICAgIGZsb2F0IGVkZ2VXaWR0aCA9IDAuMDI7XHJcblxyXG4gICAgICAgIGlmIChzdW0gPj0gdGhyZXNob2xkIC0gZWRnZVdpZHRoKSB7XHJcbiAgICAgICAgICBmbG9hdCBpbnRlbnNpdHkgPSBtaW4oMS4wLCAoc3VtIC0gdGhyZXNob2xkKSAqIDIwLjApO1xyXG4gICAgICAgICAgdmVjMyBjb2xvciA9IG1peCh1X2JsdWVDb2xvciwgdV93aGl0ZUNvbG9yLCBpbnRlbnNpdHkpO1xyXG4gICAgICAgICAgZmxvYXQgYWxwaGEgPSBzbW9vdGhzdGVwKHRocmVzaG9sZCAtIGVkZ2VXaWR0aCwgdGhyZXNob2xkICsgZWRnZVdpZHRoLCBzdW0pO1xyXG4gICAgICAgICAgZ2xfRnJhZ0NvbG9yID0gdmVjNChjb2xvciwgYWxwaGEpO1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZ2xfRnJhZ0NvbG9yID0gdmVjNCh1X2JhY2tncm91bmRDb2xvciwgMS4wKTtcclxuICAgICAgfVxyXG4gICAgYFxyXG5cclxuICAgIC8vIEhlbHBlciBmdW5jdGlvbnMgZm9yIFdlYkdMXHJcbiAgICBjb25zdCBjb21waWxlU2hhZGVyID0gKGdsOiBXZWJHTFJlbmRlcmluZ0NvbnRleHQsIHNoYWRlclNvdXJjZTogc3RyaW5nLCBzaGFkZXJUeXBlOiBudW1iZXIpID0+IHtcclxuICAgICAgY29uc3Qgc2hhZGVyID0gZ2wuY3JlYXRlU2hhZGVyKHNoYWRlclR5cGUpXHJcbiAgICAgIGlmICghc2hhZGVyKSB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZCBub3QgY3JlYXRlIHNoYWRlclwiKVxyXG5cclxuICAgICAgZ2wuc2hhZGVyU291cmNlKHNoYWRlciwgc2hhZGVyU291cmNlKVxyXG4gICAgICBnbC5jb21waWxlU2hhZGVyKHNoYWRlcilcclxuXHJcbiAgICAgIGlmICghZ2wuZ2V0U2hhZGVyUGFyYW1ldGVyKHNoYWRlciwgZ2wuQ09NUElMRV9TVEFUVVMpKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiU2hhZGVyIGNvbXBpbGUgZmFpbGVkIHdpdGg6IFwiICsgZ2wuZ2V0U2hhZGVySW5mb0xvZyhzaGFkZXIpKVxyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiBzaGFkZXJcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBnZXRVbmlmb3JtTG9jYXRpb24gPSAoZ2w6IFdlYkdMUmVuZGVyaW5nQ29udGV4dCwgcHJvZ3JhbTogV2ViR0xQcm9ncmFtLCBuYW1lOiBzdHJpbmcpID0+IHtcclxuICAgICAgY29uc3QgdW5pZm9ybUxvY2F0aW9uID0gZ2wuZ2V0VW5pZm9ybUxvY2F0aW9uKHByb2dyYW0sIG5hbWUpXHJcbiAgICAgIGlmICh1bmlmb3JtTG9jYXRpb24gPT09IC0xKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQ2FuIG5vdCBmaW5kIHVuaWZvcm0gXCIgKyBuYW1lKVxyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiB1bmlmb3JtTG9jYXRpb25cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBnZXRBdHRyaWJMb2NhdGlvbiA9IChnbDogV2ViR0xSZW5kZXJpbmdDb250ZXh0LCBwcm9ncmFtOiBXZWJHTFByb2dyYW0sIG5hbWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBjb25zdCBhdHRyaWJ1dGVMb2NhdGlvbiA9IGdsLmdldEF0dHJpYkxvY2F0aW9uKHByb2dyYW0sIG5hbWUpXHJcbiAgICAgIGlmIChhdHRyaWJ1dGVMb2NhdGlvbiA9PT0gLTEpIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW4gbm90IGZpbmQgYXR0cmlidXRlIFwiICsgbmFtZSlcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gYXR0cmlidXRlTG9jYXRpb25cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB2ZXJ0ZXhTaGFkZXIgPSBjb21waWxlU2hhZGVyKGdsLCB2ZXJ0ZXhTaGFkZXJTcmMsIGdsLlZFUlRFWF9TSEFERVIpXHJcbiAgICBjb25zdCBmcmFnbWVudFNoYWRlciA9IGNvbXBpbGVTaGFkZXIoZ2wsIGZyYWdtZW50U2hhZGVyU3JjLCBnbC5GUkFHTUVOVF9TSEFERVIpXHJcbiAgICBjb25zdCBwcm9ncmFtID0gZ2wuY3JlYXRlUHJvZ3JhbSgpXHJcbiAgICBpZiAoIXByb2dyYW0pIHRocm93IG5ldyBFcnJvcihcIkNvdWxkIG5vdCBjcmVhdGUgcHJvZ3JhbVwiKVxyXG5cclxuICAgIGdsLmF0dGFjaFNoYWRlcihwcm9ncmFtLCB2ZXJ0ZXhTaGFkZXIpXHJcbiAgICBnbC5hdHRhY2hTaGFkZXIocHJvZ3JhbSwgZnJhZ21lbnRTaGFkZXIpXHJcbiAgICBnbC5saW5rUHJvZ3JhbShwcm9ncmFtKVxyXG5cclxuICAgIGNvbnN0IHZlcnRleERhdGEgPSBuZXcgRmxvYXQzMkFycmF5KFtcclxuICAgICAgLTEuMCwgMS4wLCAvLyB0b3AgbGVmdFxyXG4gICAgICAtMS4wLCAtMS4wLCAvLyBib3R0b20gbGVmdFxyXG4gICAgICAxLjAsIDEuMCwgLy8gdG9wIHJpZ2h0XHJcbiAgICAgIDEuMCwgLTEuMCwgLy8gYm90dG9tIHJpZ2h0XHJcbiAgICBdKVxyXG5cclxuICAgIGNvbnN0IHZlcnRleERhdGFCdWZmZXIgPSBnbC5jcmVhdGVCdWZmZXIoKVxyXG4gICAgZ2wuYmluZEJ1ZmZlcihnbC5BUlJBWV9CVUZGRVIsIHZlcnRleERhdGFCdWZmZXIpXHJcbiAgICBnbC5idWZmZXJEYXRhKGdsLkFSUkFZX0JVRkZFUiwgdmVydGV4RGF0YSwgZ2wuU1RBVElDX0RSQVcpXHJcblxyXG4gICAgY29uc3QgcG9zaXRpb25IYW5kbGUgPSBnZXRBdHRyaWJMb2NhdGlvbihnbCwgcHJvZ3JhbSwgXCJwb3NpdGlvblwiKVxyXG4gICAgZ2wuZW5hYmxlVmVydGV4QXR0cmliQXJyYXkocG9zaXRpb25IYW5kbGUpXHJcbiAgICBnbC52ZXJ0ZXhBdHRyaWJQb2ludGVyKHBvc2l0aW9uSGFuZGxlLCAyLCBnbC5GTE9BVCwgZmFsc2UsIDIgKiA0LCAwKVxyXG5cclxuICAgIGNvbnN0IG1ldGFiYWxsc0hhbmRsZSA9IGdldFVuaWZvcm1Mb2NhdGlvbihnbCwgcHJvZ3JhbSwgXCJtZXRhYmFsbHNcIilcclxuICAgIGNvbnN0IG51bU1ldGFiYWxsc0hhbmRsZSA9IGdldFVuaWZvcm1Mb2NhdGlvbihnbCwgcHJvZ3JhbSwgXCJ1X251bU1ldGFiYWxsc1wiKTtcclxuICAgIGNvbnN0IHdpZHRoSGFuZGxlID0gZ2V0VW5pZm9ybUxvY2F0aW9uKGdsLCBwcm9ncmFtLCBcInVfd2lkdGhcIik7XHJcbiAgICBjb25zdCBoZWlnaHRIYW5kbGUgPSBnZXRVbmlmb3JtTG9jYXRpb24oZ2wsIHByb2dyYW0sIFwidV9oZWlnaHRcIik7XHJcbiAgICBjb25zdCBibHVlQ29sb3JIYW5kbGUgPSBnZXRVbmlmb3JtTG9jYXRpb24oZ2wsIHByb2dyYW0sIFwidV9ibHVlQ29sb3JcIik7XHJcbiAgICBjb25zdCB3aGl0ZUNvbG9ySGFuZGxlID0gZ2V0VW5pZm9ybUxvY2F0aW9uKGdsLCBwcm9ncmFtLCBcInVfd2hpdGVDb2xvclwiKTtcclxuICAgIGNvbnN0IGJhY2tncm91bmRDb2xvckhhbmRsZSA9IGdldFVuaWZvcm1Mb2NhdGlvbihnbCwgcHJvZ3JhbSwgXCJ1X2JhY2tncm91bmRDb2xvclwiKTtcclxuXHJcbiAgICBjb25zdCBsb29wID0gKCkgPT4ge1xyXG4gICAgICAvLyBHZXQgc3VidGxlIHNjcm9sbCBwaHlzaWNzIGVmZmVjdFxyXG4gICAgICBjb25zdCBzY3JvbGxFZmZlY3QgPSBzY3JvbGxQaHlzaWNzUmVmLmN1cnJlbnQuZWZmZWN0XHJcbiAgICAgIGNvbnN0IHRpbWUgPSBEYXRlLm5vdygpICogMC4wMDEgLy8gQ29udmVydCB0byBzZWNvbmRzXHJcblxyXG4gICAgICAvLyBVcGRhdGUgbWV0YWJhbGxzIHdpdGggY29udGludW91cyBtb3ZlbWVudCBhbmQgc2Nyb2xsLXJlc3BvbnNpdmUgZWZmZWN0c1xyXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IG51bU1ldGFiYWxsczsgaSsrKSB7XHJcbiAgICAgICAgY29uc3QgbWV0YWJhbGwgPSBtZXRhYmFsbHNbaV1cclxuXHJcbiAgICAgICAgLy8gQmFzZSBjb250aW51b3VzIG1vdmVtZW50IC0gYWx3YXlzIGFjdGl2ZSAoc2xvd2VkIGRvd24pXHJcbiAgICAgICAgY29uc3QgYmFzZVNwZWVkID0gYW5pbWF0aW9uU2V0dGluZ3Muc3BlZWQgKiAwLjA3NyAvLyAxNSUgb2Ygb3JpZ2luYWwgc3BlZWQgYXMgYmFzZSAoaGFsZiBvZiBwcmV2aW91cylcclxuICAgICAgICBjb25zdCBzdGF0aWNXaWdnbGVYID0gTWF0aC5zaW4odGltZSAqIDAuMjUgKyBpICogMS4yKSAqIGJhc2VTcGVlZCAqIDAuNCAvLyBIYWxmIGZyZXF1ZW5jeVxyXG4gICAgICAgIGNvbnN0IHN0YXRpY1dpZ2dsZVkgPSBNYXRoLmNvcyh0aW1lICogMC4xNSArIGkgKiAwLjgpICogYmFzZVNwZWVkICogMC4zIC8vIEhhbGYgZnJlcXVlbmN5XHJcblxyXG4gICAgICAgIC8vIEFwcGx5IGJhc2UgbW92ZW1lbnQgd2l0aCBzdGF0aWMgd2lnZ2xlIChyZWR1Y2VkIGludGVuc2l0eSlcclxuICAgICAgICBsZXQgbmV3VnggPSBtZXRhYmFsbC52eCArIHN0YXRpY1dpZ2dsZVggKiAwLjA2XHJcbiAgICAgICAgbGV0IG5ld1Z5ID0gbWV0YWJhbGwudnkgKyBzdGF0aWNXaWdnbGVZICogMC4wNVxyXG5cclxuICAgICAgICAvLyBBZGQgc2Nyb2xsIHBoeXNpY3Mgb24gdG9wIG9mIGJhc2UgbW92ZW1lbnRcclxuICAgICAgICBpZiAoTWF0aC5hYnMoc2Nyb2xsRWZmZWN0KSA+IDAuMDEpIHtcclxuICAgICAgICAgIC8vIFN1YnRsZSB2ZXJ0aWNhbCBkcmlmdCBiYXNlZCBvbiBzY3JvbGwgZGlyZWN0aW9uXHJcbiAgICAgICAgICBuZXdWeSArPSBzY3JvbGxFZmZlY3QgKiAwLjI1XHJcblxyXG4gICAgICAgICAgLy8gRW5oYW5jZWQgaG9yaXpvbnRhbCB3aWdnbGUgZHVyaW5nIHNjcm9sbCAoc2xvd2VkIGRvd24pXHJcbiAgICAgICAgICBjb25zdCBzY3JvbGxXaWdnbGUgPSBNYXRoLnNpbih0aW1lICogMSArIGkgKiAwLjcpICogTWF0aC5hYnMoc2Nyb2xsRWZmZWN0KSAqIDAuMSAvLyBIYWxmIGZyZXF1ZW5jeVxyXG4gICAgICAgICAgbmV3VnggKz0gc2Nyb2xsV2lnZ2xlXHJcblxyXG4gICAgICAgICAgLy8gVGlueSBiaXQgb2YgcmFuZG9tbmVzcyBmb3IgbmF0dXJhbCB2YXJpYXRpb25cclxuICAgICAgICAgIG5ld1Z4ICs9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIE1hdGguYWJzKHNjcm9sbEVmZmVjdCkgKiAwLjA1XHJcbiAgICAgICAgICBuZXdWeSArPSAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiBNYXRoLmFicyhzY3JvbGxFZmZlY3QpICogMC4wM1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gRW5zdXJlIG1pbmltdW0gbW92ZW1lbnQgdG8gcHJldmVudCBzdGFnbmF0aW9uXHJcbiAgICAgICAgY29uc3QgbWluU3BlZWQgPSAwLjJcclxuICAgICAgICBpZiAoTWF0aC5hYnMobmV3VngpIDwgbWluU3BlZWQpIG5ld1Z4ICs9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIG1pblNwZWVkXHJcbiAgICAgICAgaWYgKE1hdGguYWJzKG5ld1Z5KSA8IG1pblNwZWVkKSBuZXdWeSArPSAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiBtaW5TcGVlZFxyXG5cclxuICAgICAgICAvLyBVcGRhdGUgcG9zaXRpb24gd2l0aCBtb2RpZmllZCB2ZWxvY2l0aWVzXHJcbiAgICAgICAgbWV0YWJhbGwueCArPSBuZXdWeFxyXG4gICAgICAgIG1ldGFiYWxsLnkgKz0gbmV3VnlcclxuXHJcbiAgICAgICAgLy8gU3RvcmUgYmFjayB0aGUgdmVsb2NpdGllcyB3aXRoIGxlc3MgYWdncmVzc2l2ZSBkYW1waW5nXHJcbiAgICAgICAgbWV0YWJhbGwudnggPSBuZXdWeCAqIDAuOTk1IC8vIExlc3MgZGFtcGluZyB0byBtYWludGFpbiBtb3ZlbWVudFxyXG4gICAgICAgIG1ldGFiYWxsLnZ5ID0gbmV3VnkgKiAwLjk5NVxyXG5cclxuICAgICAgICAvLyBCb3VuZGFyeSBjb2xsaXNpb25cclxuICAgICAgICBpZiAobWV0YWJhbGwueCA8IG1ldGFiYWxsLnIgfHwgbWV0YWJhbGwueCA+IHdpZHRoIC0gbWV0YWJhbGwucikgbWV0YWJhbGwudnggKj0gLTFcclxuICAgICAgICBpZiAobWV0YWJhbGwueSA8IG1ldGFiYWxsLnIgfHwgbWV0YWJhbGwueSA+IGhlaWdodCAtIG1ldGFiYWxsLnIpIG1ldGFiYWxsLnZ5ICo9IC0xXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGRhdGFUb1NlbmRUb0dQVSA9IG5ldyBGbG9hdDMyQXJyYXkoMyAqIG51bU1ldGFiYWxscylcclxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBudW1NZXRhYmFsbHM7IGkrKykge1xyXG4gICAgICAgIGNvbnN0IGJhc2VJbmRleCA9IDMgKiBpXHJcbiAgICAgICAgY29uc3QgbWIgPSBtZXRhYmFsbHNbaV1cclxuICAgICAgICBkYXRhVG9TZW5kVG9HUFVbYmFzZUluZGV4ICsgMF0gPSBtYi54XHJcbiAgICAgICAgZGF0YVRvU2VuZFRvR1BVW2Jhc2VJbmRleCArIDFdID0gbWIueVxyXG4gICAgICAgIGRhdGFUb1NlbmRUb0dQVVtiYXNlSW5kZXggKyAyXSA9IG1iLnJcclxuICAgICAgfVxyXG5cclxuICAgICAgZ2wudW5pZm9ybTNmdihtZXRhYmFsbHNIYW5kbGUsIGRhdGFUb1NlbmRUb0dQVSlcclxuICAgICAgZ2wudW5pZm9ybTFpKG51bU1ldGFiYWxsc0hhbmRsZSwgbnVtTWV0YWJhbGxzKTsgLy8gU2V0IG51bU1ldGFiYWxscyB1bmlmb3JtXHJcbiAgICAgIGdsLnVuaWZvcm0xZih3aWR0aEhhbmRsZSwgd2lkdGgpO1xyXG4gICAgICBnbC51bmlmb3JtMWYoaGVpZ2h0SGFuZGxlLCBoZWlnaHQpO1xyXG4gICAgICBnbC51bmlmb3JtM2YoYmx1ZUNvbG9ySGFuZGxlLCBhbmltYXRpb25TZXR0aW5ncy5ibHVlQ29sb3IuciwgYW5pbWF0aW9uU2V0dGluZ3MuYmx1ZUNvbG9yLmcsIGFuaW1hdGlvblNldHRpbmdzLmJsdWVDb2xvci5iKTtcclxuICAgICAgZ2wudW5pZm9ybTNmKHdoaXRlQ29sb3JIYW5kbGUsIGFuaW1hdGlvblNldHRpbmdzLndoaXRlQ29sb3IuciwgYW5pbWF0aW9uU2V0dGluZ3Mud2hpdGVDb2xvci5nLCBhbmltYXRpb25TZXR0aW5ncy53aGl0ZUNvbG9yLmIpO1xyXG4gICAgICBnbC51bmlmb3JtM2YoYmFja2dyb3VuZENvbG9ySGFuZGxlLCBhbmltYXRpb25TZXR0aW5ncy5iYWNrZ3JvdW5kQ29sb3IuciwgYW5pbWF0aW9uU2V0dGluZ3MuYmFja2dyb3VuZENvbG9yLmcsIGFuaW1hdGlvblNldHRpbmdzLmJhY2tncm91bmRDb2xvci5iKTtcclxuICAgICAgZ2wuZHJhd0FycmF5cyhnbC5UUklBTkdMRV9TVFJJUCwgMCwgNClcclxuXHJcbiAgICAgIGFuaW1hdGlvblJlZi5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGxvb3ApXHJcbiAgICB9XHJcblxyXG4gICAgLy8gTW91c2UgaW50ZXJhY3Rpb25cclxuICAgIGNvbnN0IGhhbmRsZU1vdXNlTW92ZSA9IChlOiBNb3VzZUV2ZW50KSA9PiB7XHJcbiAgICAgIGNvbnN0IHJlY3QgPSBjYW52YXMuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KClcclxuICAgICAgbW91c2UueCA9IGUuY2xpZW50WCAtIHJlY3QubGVmdFxyXG4gICAgICBtb3VzZS55ID0gZS5jbGllbnRZIC0gcmVjdC50b3BcclxuICAgIH1cclxuXHJcbiAgICBjYW52YXMuYWRkRXZlbnRMaXN0ZW5lcihcIm1vdXNlbW92ZVwiLCBoYW5kbGVNb3VzZU1vdmUpXHJcblxyXG4gICAgLy8gSGFuZGxlIHJlc2l6ZVxyXG4gICAgY29uc3QgaGFuZGxlUmVzaXplID0gKCkgPT4ge1xyXG4gICAgICBjb25zdCBuZXdEcHIgPSB3aW5kb3cuZGV2aWNlUGl4ZWxSYXRpbyB8fCAxO1xyXG4gICAgICBjYW52YXMud2lkdGggPSB3aW5kb3cuaW5uZXJXaWR0aCAqIG5ld0RwcjtcclxuICAgICAgY2FudmFzLmhlaWdodCA9IHdpbmRvdy5pbm5lckhlaWdodCAqIG5ld0RwcjtcclxuICAgICAgZ2wudmlld3BvcnQoMCwgMCwgY2FudmFzLndpZHRoLCBjYW52YXMuaGVpZ2h0KTtcclxuICAgIH1cclxuXHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBoYW5kbGVSZXNpemUpXHJcblxyXG4gICAgZ2wudXNlUHJvZ3JhbShwcm9ncmFtKTtcclxuICAgIGxvb3AoKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAoYW5pbWF0aW9uUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShhbmltYXRpb25SZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuICAgICAgY2FudmFzLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJtb3VzZW1vdmVcIiwgaGFuZGxlTW91c2VNb3ZlKTtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJyZXNpemVcIiwgaGFuZGxlUmVzaXplKTtcclxuICAgIH07XHJcbiAgfSwgW10pXHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZSByZWxhdGl2ZSBvdmVyZmxvdy14LWhpZGRlblwiPlxyXG4gICAgICB7LyogV2ViR0wgQ2FudmFzIEJhY2tncm91bmQgKi99XHJcbiAgICAgIDxjYW52YXNcclxuICAgICAgICByZWY9e2NhbnZhc1JlZn1cclxuICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHctZnVsbCBoLWZ1bGwgcG9pbnRlci1ldmVudHMtbm9uZVwiXHJcbiAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgIHpJbmRleDogMSxcclxuICAgICAgICAgIHRyYW5zZm9ybTogYHRyYW5zbGF0ZVkoJHtzY3JvbGxZICogMC4zfXB4KWAgLy8gUGFyYWxsYXggZWZmZWN0XHJcbiAgICAgICAgfX1cclxuICAgICAgLz5cclxuXHJcblxyXG5cclxuICAgICAgey8qIE5hdmlnYXRpb24gKi99XHJcbiAgICAgIDxuYXYgY2xhc3NOYW1lPXtgYmctd2hpdGUvOTUgYmFja2Ryb3AtYmx1ci1tZCBib3JkZXItYiBib3JkZXItZ3JheS0xMDAgc3RpY2t5IHRvcC0wIHotNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XHJcbiAgICAgICAgc2Nyb2xsWSA+IDUwID8gJ3NoYWRvdy1sZycgOiAnc2hhZG93LXNtJ1xyXG4gICAgICB9YH0+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXItd2lkZVwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gaC0xNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS02MDAgdG8tYmx1ZS03MDAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtbGdcIj5GPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgZm9udC1ib2xkIHRleHQteGwgdHJhY2tpbmctdGlnaHRcIj5Gb3JjZWZpPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtOFwiPlxyXG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LWJsdWUtNjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJlbGF0aXZlIGdyb3VwXCI+XHJcbiAgICAgICAgICAgICAgICBQcm9qZWN0c1xyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIGxlZnQtMCB3LTAgaC0wLjUgYmctYmx1ZS02MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGdyb3VwLWhvdmVyOnctZnVsbFwiPjwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtYmx1ZS02MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHRleHQtc20gZm9udC1tZWRpdW0gcmVsYXRpdmUgZ3JvdXBcIj5cclxuICAgICAgICAgICAgICAgIEN1cmF0b3JzXHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTEgbGVmdC0wIHctMCBoLTAuNSBiZy1ibHVlLTYwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZ3JvdXAtaG92ZXI6dy1mdWxsXCI+PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ibHVlLTYwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdGV4dC1zbSBmb250LW1lZGl1bSByZWxhdGl2ZSBncm91cFwiPlxyXG4gICAgICAgICAgICAgICAgQ29tbXVuaXR5XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTEgbGVmdC0wIHctMCBoLTAuNSBiZy1ibHVlLTYwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZ3JvdXAtaG92ZXI6dy1mdWxsXCI+PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ibHVlLTYwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdGV4dC1zbSBmb250LW1lZGl1bSByZWxhdGl2ZSBncm91cFwiPlxyXG4gICAgICAgICAgICAgICAgRG9jc1xyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIGxlZnQtMCB3LTAgaC0wLjUgYmctYmx1ZS02MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGdyb3VwLWhvdmVyOnctZnVsbFwiPjwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgICAgIDxCdXR0b24gY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWJsdWUtNzAwIGhvdmVyOmZyb20tYmx1ZS03MDAgaG92ZXI6dG8tYmx1ZS04MDAgdGV4dC13aGl0ZSBweC02IHB5LTIuNSB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1sZyBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1XCI+XHJcbiAgICAgICAgICAgICAgICA8V2FsbGV0IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICBDb25uZWN0IFdhbGxldFxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L25hdj5cclxuXHJcbiAgICAgIHsvKiBIZXJvIEJhbm5lciAqL31cclxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicmVsYXRpdmUgcHktMjAgbGc6cHktMzIgbWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0byBweC02IHRleHQtY2VudGVyIHJlbGF0aXZlIHotMTBcIj5cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtZGlzcGxheSB0ZXh0LWdyYXktOTAwIG1iLTggbWF4LXctNHhsIG14LWF1dG9cIj5cclxuICAgICAgICAgICAgRGlzY292ZXIgJiBTdXBwb3J0e1wiIFwifVxyXG4gICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB2aWEtYmx1ZS03MDAgdG8tcHVycGxlLTYwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgIGZpbHRlcjogXCJkcm9wLXNoYWRvdygwIDAgMjBweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC4zKSlcIixcclxuICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogXCJ3ZWIzUHVsc2UgM3MgZWFzZS1pbi1vdXQgaW5maW5pdGVcIixcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgV2ViMyBTdGFydHVwc1xyXG4gICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgXHJcblxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ib2R5LWxhcmdlIHRleHQtZ3JheS02MDAgbWItMTIgbWF4LXctNHhsIG14LWF1dG9cIj5cclxuICAgICAgICAgICAgRm9yY2VmaSBpcyBhIGRlY2VudHJhbGl6ZWQgbGF1bmNocGFkIHRoYXQgYnJpZGdlcyBpbmR1c3RyeSBleHBlcnRzIHdpdGggV2ViMyBTdGFydHVwcy4gXHJcbiAgICAgICAgICAgIExpc3QgeW91ciBlYXJseS1zdGFnZSBwcm9qZWN0LCBvciBtb25ldGl6ZSB5b3VyIHNraWxscyBhcyBhIHBsYXRmb3JtIGN1cmF0b3IuXHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTEwXCI+XHJcbiAgICAgICAgICAgIDxKb2luQ29ob3J0U3BsaXRCdXR0b24gLz5cclxuXHJcbiAgICAgICAgICAgIHsvKiBFbmhhbmNlZCBTdGF0cyAqL31cclxuICAgICAgICAgICAgey8qIFJlbW92ZWQgdGhlIDIwMCsgRXhwZXJ0IEN1cmF0b3JzIGFuZCA3MCsgTGlzdGVkIFByb2plY3RzIGNhcmRzIGFzIHJlcXVlc3RlZCAqL31cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBTb2NpYWwgTGlua3MgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBnYXAtNCBtdC04IHNtOmdhcC02XCI+IHsvKiBBZGp1c3RlZCBnYXAgZm9yIG1vYmlsZSAqL31cclxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMS41IHRleHQteHMgc206dGV4dC1zbSBmb250LW1lZGl1bVwiPiB7LyogQWRqdXN0ZWQgc3BhY2UteCBhbmQgdGV4dCBzaXplICovfVxyXG4gICAgICAgICAgICAgIDxUd2l0dGVyIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNVwiIC8+IHsvKiBBZGp1c3RlZCBpY29uIHNpemUgKi99XHJcbiAgICAgICAgICAgICAgPHNwYW4+Rm9sbG93IG9uIFg8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMS41IHRleHQteHMgc206dGV4dC1zbSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgIDxNZXNzYWdlQ2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNVwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4+Sm9pbiBEaXNjb3JkPC9zcGFuPlxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWJsdWUtNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEuNSB0ZXh0LXhzIHNtOnRleHQtc20gZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01XCIgLz5cclxuICAgICAgICAgICAgICA8c3Bhbj5SZWFkIERvY3VtZW50YXRpb248L3NwYW4+XHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMS41IHRleHQteHMgc206dGV4dC1zbSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNVwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4+Sm9pbiBUZWxlZ3JhbTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvc2VjdGlvbj5cclxuXHJcbiAgICAgIHsvKiBGaXJzdCBDb2hvcnQgSW5mb3JtYXRpb24gKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMTYgcmVsYXRpdmUgei0yMFwiIHN0eWxlPXt7XHJcbiAgICAgICAgYmFja2dyb3VuZDogXCJsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCksIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KSA3MCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMCkgMTAwJSlcIixcclxuICAgICAgICBiYWNrZHJvcEZpbHRlcjogXCJibHVyKDdweClcIlxyXG4gICAgICB9fT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTZcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIj5cclxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtZXh0cmFib2xkIHRleHQtZ3JheS05MDAgbWItNlwiPkZpcnN0IENvaG9ydCBCZW5lZml0czwvaDI+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYXgtdy0yeGwgbXgtYXV0byBsZWFkaW5nLXJlbGF4ZWRcIj5cclxuICAgICAgICAgICAgICBBcyBwYXJ0IG9mIEZvcmNlZmnigJlzIGZpcnN0IGNvaG9ydCwgeW91IHJlY2VpdmUgYSBudW1iZXIgb2YgYmVuZWZpdHMgdGhhdCBoZWxwIHNoYXBlIHlvdXIgcHJvZHVjdCBhbmQgYnVpbGQgYW4gYXVkaWVuY2UgZm9yIHlvdXIgcHJvamVjdCBhbmQgc2VydmljZXMuIEhlcmUgaXMgYSBicmVha2Rvd24gb2Ygd2hhdCB5b3UgY2FuIGV4cGVjdDpcclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1kOmdyaWQtY29scy0yIGdhcC04XCI+XHJcbiAgICAgICAgICAgIHsvKiBQcm9qZWN0cyAqL31cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0yMDAgdGV4dC1sZWZ0XCI+XHJcbiAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItNCBwdC04XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwIHRvLWJsdWUtNjAwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00IHNoYWRvd1wiPlxyXG4gICAgICAgICAgICAgICAgICA8Um9ja2V0IGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTIgdGV4dC1jZW50ZXJcIj5Qcm9qZWN0czwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicGItNiBweC04XCI+XHJcbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHRleHQtZ3JheS03MDAgbGVhZGluZy1yZWxheGVkIGxpc3QtZGlzYyBsaXN0LWluc2lkZSBzcGFjZS15LTIgdGV4dC1sZWZ0XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT5CaXdlZWtseSBmZWVkYmFjayBzZXNzaW9ucyBvbiBwcm9kdWN0LCBtYXJrZXRpbmcsIGFuZCBCRDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT5Db250ZXh0dWFsbHkgcmVsZXZhbnQgcHJvZHVjdCBkZWVwZGl2ZSAmIGFkdmljZTwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT5FY29zeXN0ZW0gaW50cm9zIGFuZCByZWxldmFudCBwYXJ0bmVyc2hpcCBvcHBvcnR1bml0aWVzPC9saT5cclxuICAgICAgICAgICAgICAgICAgPGxpPkVkdWNhdGlvbmFsIG1hdGVyaWFscyBhbmQgZGlzY291bnRzIG9uIGV4dGVybmFsIGNvdXJzZXM8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+TWlsZXN0b25lLWRlcGVuZGVudCBzdXBwb3J0IGluIHRva2VuIGdyYW50czwvbGk+XHJcbiAgICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgIHsvKiBDdXJhdG9ycyAqL31cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0yMDAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi00IHB0LThcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTUwMCB0by1wdXJwbGUtNjAwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00IHNoYWRvd1wiPlxyXG4gICAgICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTIgdGV4dC1jZW50ZXJcIj5DdXJhdG9yczwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicGItNiBweC04XCI+XHJcbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHRleHQtZ3JheS03MDAgbGVhZGluZy1yZWxheGVkIGxpc3QtZGlzYyBsaXN0LWluc2lkZSBzcGFjZS15LTIgdGV4dC1sZWZ0XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT5JbnRyb3MgdG8gcHJvamVjdHMgdGhhdCByZXF1aXJlIHlvdXIgc2VydmljZXM8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+TW9uZXRpemF0aW9uIG9wcG9ydHVuaXRpZXMgdGhyb3VnaCBwbGF0Zm9ybSBwYXJ0aWNpcGF0aW9uPC9saT5cclxuICAgICAgICAgICAgICAgICAgPGxpPlNtYWxsIHBvb2wgb2YgY29tcGV0aXRvcnMgKG9ubHkgdGhlIGJlc3QgYXJlIGFjY2VwdGVkKTwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT5JbmNyZWFzZSB5b3VyIHJlYWNoIGFuZCBncm93IHlvdXIgYXVkaWVuY2U8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+TWlsZXN0b25lLWRlcGVuZGVudCB0b2tlbiByZXdhcmRzPC9saT5cclxuICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBBdWRpZW5jZSBTZWdtZW50cyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlY3Rpb24tc3BhY2luZyByZWxhdGl2ZSB6LTIwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci13aWRlXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTZcIj5cclxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlRocmVlIFdheXMgdG8gUGFydGljaXBhdGU8L2gyPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYm9keS1sYXJnZSB0ZXh0LWdyYXktNjAwIG1heC13LTJ4bCBteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgICBKb2luIG91ciBlY29zeXN0ZW0gYXMgYSBwcm9qZWN0LCBjdXJhdG9yLCBvciBpbnZlc3RvciBhbmQgc2hhcGUgdGhlIFdlYjMgZnV0dXJlXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMyBnYXAtOFwiPlxyXG4gICAgICAgICAgICAgIHsvKiBQcm9qZWN0cyAqL31cclxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItMCBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tc2hhZG93IGR1cmF0aW9uLTMwMCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItNiBwdC0xMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwIHRvLWJsdWUtNjAwIHJvdW5kZWQtM3hsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNiBzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICA8Um9ja2V0IGNsYXNzTmFtZT1cInctMTAgaC0xMCB0ZXh0LXdoaXRlXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlByb2plY3RzPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicGItMTBcIj5cclxuICAgICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtbGcgbGVhZGluZy1yZWxheGVkIG1iLThcIj5cclxuQWNjZXNzIG91ciB2ZXR0ZWQgbmV0d29yayBvZiA3MCsgTGlzdGVkIFByb2plY3RzIHRvIGZpbmQgdGFsZW50IGFuZCBncm93dGggb3Bwb3J0dW5pdGllcy4gQWNjZWxlcmF0ZSB5b3VyIHByb2plY3Qgd2l0aCBjb21tdW5pdHkgZXhwZXJ0aXNlLlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS03MDAgaG92ZXI6YmctYmx1ZS01MCBmb250LXNlbWlib2xkIHRleHQtYmFzZSBweC02IHB5LTMgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIExlYXJuIE1vcmUg4oaSXHJcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBDdXJhdG9ycyAqL31cclxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItMCBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tc2hhZG93IGR1cmF0aW9uLTMwMCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItNiBwdC0xMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXB1cnBsZS01MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLTN4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTYgc2hhZG93LWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5DdXJhdG9yczwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInBiLTEwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZCBtYi04XCI+XHJcblN1cHBvcnQgcHJvbWlzaW5nIHByb2plY3RzIGFuZCBlYXJuIGluY29tZS4gSm9pbiBvdXIgbmV0d29yayBvZiAyMDArIEV4cGVydCBDdXJhdG9ycyBtYWtpbmcgYSByZWFsIGltcGFjdCBpbiBXZWIzLlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBjbGFzc05hbWU9XCJ0ZXh0LXB1cnBsZS02MDAgaG92ZXI6dGV4dC1wdXJwbGUtNzAwIGhvdmVyOmJnLXB1cnBsZS01MCBmb250LXNlbWlib2xkIHRleHQtYmFzZSBweC02IHB5LTMgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIEdldCBJbnZvbHZlZCDihpJcclxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgICAgey8qIEludmVzdG9ycyAqL31cclxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItMCBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tc2hhZG93IGR1cmF0aW9uLTMwMCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItNiBwdC0xMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTUwMCB0by1ncmVlbi02MDAgcm91bmRlZC0zeGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi02IHNoYWRvdy1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxEb2xsYXJTaWduIGNsYXNzTmFtZT1cInctMTAgaC0xMCB0ZXh0LXdoaXRlXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPkludmVzdG9yczwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInBiLTEwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZCBtYi04XCI+XHJcbkludmVzdCBpbiBjdXJhdGVkIHByb2plY3RzIGF0IHRoZSBlYXJsaWVzdCBzdGFnZS4gRWFybiB0aHJvdWdoIHN0YWtpbmcgYW5kIHN1cHBvcnQgaW5ub3ZhdGl2ZSBXZWIzIHByb2plY3RzLiBBY2Nlc3MgZXhjbHVzaXZlIGVhcmx5LXN0YWdlIG9wcG9ydHVuaXRpZXMgYmFja2VkIGJ5IG91ciBleHBlcnQgY3VyYXRvcnMuXHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIGhvdmVyOnRleHQtZ3JlZW4tNzAwIGhvdmVyOmJnLWdyZWVuLTUwIGZvbnQtc2VtaWJvbGQgdGV4dC1iYXNlIHB4LTYgcHktMyByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgRXhwbG9yZSBOb3cg4oaSXHJcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBUb2tlbiBMYXVuY2ggVGVhc2VyICYgV2FpdGxpc3QgQ29tYmluZWQgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0yMCByZWxhdGl2ZSB6LTIwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbVwiPjwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBweC02IHRleHQtY2VudGVyIHJlbGF0aXZlIHotMTAgbWItMjRcIj5cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LTJ4bCBob3ZlcjpzaGFkb3ctM3hsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiPlxyXG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInB0LTEyIHBiLTZcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWdyYXktMTAwIHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNiBzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICAgICAgPFJvY2tldCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtZ3JheS02MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5Tb21ldGhpbmcgQmlnIGlzIENvbWluZzwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicGItMTJcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZCBtYi04XCI+XHJcbiAgICAgICAgICAgICAgICAgIFN0YXkgdHVuZWQgZm9yIEZvcmNlZmkncyB0b2tlbiBsYXVuY2gsIGVtcG93ZXJpbmcgb3VyIGVjb3N5c3RlbS4gRGV0YWlscyBzb29uIVxyXG4gICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XHJcbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHB4LTYgdGV4dC1jZW50ZXIgcmVsYXRpdmUgei0xMFwiPlxyXG4gICAgICAgICAgICB7LyogV2FpdGxpc3QgUGFydCAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBtYi0xMlwiPlxyXG4gICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwidy0xMCBoLTEwIHRleHQtZ3JheS02MDBcIiAvPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtdC00XCI+Sm9pbiB0aGUgV2FpdGxpc3Q8L2gzPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtdC0yXCI+QmUgdGhlIGZpcnN0IHRvIGtub3cgd2hlbiB3ZSBsYXVuY2g8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNlwiPkJlIHRoZSBGaXJzdCB0byBFeHBlcmllbmNlIEZvcmNlZmk8L2gyPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWItMTIgbWF4LXctM3hsIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XHJcbiAgICAgICAgICAgICAgRm9sbG93IG91ciBzb2NpYWxzIGFuZCBsZWFybiBtb3JlIGFib3V0IG91ciBwbGF0Zm9ybSB0byBiZW5lZml0IGFzIGFuIGVhcmx5LXN0YWdlIHVzZXIuIElmIHlvdSBoYXZlIHF1ZXN0aW9ucyBmZWVsIGZyZWUgdG8gcmVhY2ggb3V0IGFuZCBvdXIgdGVhbSB3aWxsIGdldCBiYWNrIHRvIHlvdS5cclxuICAgICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgICAgey8qIFdhaXRsaXN0IFNpZ251cCAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCBteC1hdXRvIG1iLTEyIHB4LTQgc206cHgtMFwiPiB7LyogQWRkZWQgaG9yaXpvbnRhbCBwYWRkaW5nIGZvciBzbWFsbCBzY3JlZW5zICovfVxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtM1wiPiB7LyogQ2hhbmdlZCB0byBmbGV4LWNvbCBvbiBtb2JpbGUsIGZsZXgtcm93IG9uIHNtKyAqL31cclxuICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICBpZD1cImVtYWlsSW5wdXRcIlxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItZ3JheS0zMDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwIHRleHQtZ3JheS05MDAgcGxhY2Vob2xkZXI6dGV4dC1ncmF5LTUwMCB0ZXh0LWJhc2Ugc2hhZG93LXNtIGZsZXgtZ3Jvd1wiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHB4LTYgd2hpdGVzcGFjZS1ub3dyYXAgZm9udC1zZW1pYm9sZCBzaGFkb3ctbGcgdy1mdWxsIHNtOnctYXV0b1wiIC8vIE1hZGUgYnV0dG9uIGZ1bGwgd2lkdGggb24gbW9iaWxlXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBlbWFpbElucHV0ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2VtYWlsSW5wdXQnKSBhcyBIVE1MSW5wdXRFbGVtZW50O1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVtYWlsID0gZW1haWxJbnB1dC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBlbWFpbFJlZ2V4ID0gL15bXlxcc0BdK0BbXlxcc0BdK1xcLlteXFxzQF0rJC87XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChlbWFpbFJlZ2V4LnRlc3QoZW1haWwpKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBidXR0b24gPSBlbWFpbElucHV0Lm5leHRFbGVtZW50U2libGluZyBhcyBIVE1MQnV0dG9uRWxlbWVudDtcclxuICAgICAgICAgICAgICAgICAgICAgIGJ1dHRvbi50ZXh0Q29udGVudCA9ICdTdWJzY3JpYmVkJztcclxuICAgICAgICAgICAgICAgICAgICAgIGJ1dHRvbi5jbGFzc0xpc3QucmVtb3ZlKCdiZy1ibHVlLTYwMCcsICdob3ZlcjpiZy1ibHVlLTcwMCcpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgYnV0dG9uLmNsYXNzTGlzdC5hZGQoJ2JnLWdyZWVuLTYwMCcsICdob3ZlcjpiZy1ncmVlbi03MDAnKTtcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWxlcnQoJ1BsZWFzZSBlbnRlciBhIHZhbGlkIGVtYWlsIGFkZHJlc3MuJyk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICBKb2luIHRoZSBXYWl0bGlzdFxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBGb290ZXIgKi99XHJcbiAgICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCBweS0xNiByZWxhdGl2ZSB6LTIwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTZcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBzcGFjZS15LTQgbWQ6c3BhY2UteS0wIG1kOnNwYWNlLXgtOFwiPlxyXG4gICAgICAgICAgICAgIHsvKiBMb2dvICovfVxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICA8aW1nXHJcbiAgICAgICAgICAgICAgICAgICAgc3JjPVwiL2xvZ28uc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJGb3JjZWZpIExvZ29cIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvbnRhaW5cIlxyXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGZpbHRlcjogJ2JyaWdodG5lc3MoMCkgaW52ZXJ0KDEpJyB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LTJ4bFwiPkZvcmNlZmk8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBTb2NpYWwgTGlua3MgLSBIb3Jpem9udGFsIFJvdyAqL31cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNlwiPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8VHdpdHRlciBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkZvbGxvdyBvbiBYPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8TWVzc2FnZUNpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkpvaW4gRGlzY29yZDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cclxuICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+UmVhZCBEb2N1bWVudGF0aW9uPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8U2VuZCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkpvaW4gVGVsZWdyYW08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBDb3B5cmlnaHQgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj7CqSAyMDI1IEZvcmNlZmkuIEFsbCByaWdodHMgcmVzZXJ2ZWQuPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9mb290ZXI+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPHN0eWxlIGpzeD57YFxyXG4gICAgICAgIEBrZXlmcmFtZXMgd2ViM1B1bHNlIHtcclxuICAgICAgICAgIDAlLCAxMDAlIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcclxuICAgICAgICAgICAgZmlsdGVyOiBicmlnaHRuZXNzKDEuMSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICA1MCUge1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDIpO1xyXG4gICAgICAgICAgICBmaWx0ZXI6IGJyaWdodG5lc3MoMS4zKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5ob2xvZ3JhcGhpYy1jYXJkIHtcclxuICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogIzExMTtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjVzIGVhc2U7XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDEwcHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMik7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuaG9sb2dyYXBoaWMtY2FyZDpob3ZlciB7XHJcbiAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xyXG4gICAgICAgICAgYm94LXNoYWRvdzogMCAwIDIwcHggcmdiYSgwLDI1NSwyNTUsMC41KSwgMCAxMHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjMpO1xyXG4gICAgICAgIH1cclxuICAgICAgYH08L3N0eWxlPlxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJCdXR0b24iLCJJbnB1dCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDb2hvcnRJbmZvTW9kYWwiLCJBcnJvd1JpZ2h0IiwiV2FsbGV0IiwiVHdpdHRlciIsIk1lc3NhZ2VDaXJjbGUiLCJGaWxlVGV4dCIsIlNlbmQiLCJEb2xsYXJTaWduIiwiUm9ja2V0IiwiU2hpZWxkIiwiTGluayIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiSm9pbkNvaG9ydFNwbGl0QnV0dG9uIiwic2VsZWN0ZWRPcHRpb24iLCJzZXRTZWxlY3RlZE9wdGlvbiIsImlzTW9kYWxPcGVuIiwic2V0SXNNb2RhbE9wZW4iLCJyZXNldEZvcm0iLCJzZXRSZXNldEZvcm0iLCJoYW5kbGVTcGxpdCIsImhhbmRsZUNsb3NlTW9kYWwiLCJyZXNldFRoZUZvcm0iLCJidXR0b25zIiwiaWNvbiIsImxhYmVsIiwiYWN0aW9uIiwidG9vbHRpcCIsImRpdiIsImNsYXNzTmFtZSIsImlzT3BlbiIsIm9uQ2xvc2VBY3Rpb24iLCJvbkNsaWNrIiwiSG9tZVBhZ2UiLCJjYW52YXNSZWYiLCJhbmltYXRpb25SZWYiLCJzY3JvbGxZIiwic2V0U2Nyb2xsWSIsInNjcm9sbFBoeXNpY3NSZWYiLCJ2ZWxvY2l0eSIsImxhc3RZIiwibGFzdFRpbWUiLCJlZmZlY3QiLCJhbmltYXRpb25TZXR0aW5ncyIsIm51bU1ldGFiYWxscyIsImVkZ2VXaWR0aCIsInNwZWVkIiwidGhyZXNob2xkIiwiaW50ZW5zaXR5IiwiYmx1ZUNvbG9yIiwiciIsImciLCJiIiwid2hpdGVDb2xvciIsImJhY2tncm91bmRDb2xvciIsImhhbmRsZVNjcm9sbCIsImN1cnJlbnRUaW1lIiwicGVyZm9ybWFuY2UiLCJub3ciLCJjdXJyZW50WSIsIndpbmRvdyIsInBoeXNpY3MiLCJjdXJyZW50IiwiZGVsdGFUaW1lIiwiZGVsdGFZIiwibmV3VmVsb2NpdHkiLCJtYXhFZmZlY3QiLCJNYXRoIiwibWF4IiwibWluIiwiYWRkRXZlbnRMaXN0ZW5lciIsInBhc3NpdmUiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZGVjYXlJbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImNhbnZhcyIsImRwciIsImRldmljZVBpeGVsUmF0aW8iLCJjc3NXaWR0aCIsImlubmVyV2lkdGgiLCJjc3NIZWlnaHQiLCJpbm5lckhlaWdodCIsIndpZHRoIiwiaGVpZ2h0IiwiZ2wiLCJnZXRDb250ZXh0IiwiY29uc29sZSIsImVycm9yIiwidmlld3BvcnQiLCJsb2ciLCJtb3VzZSIsIngiLCJ5IiwibWV0YWJhbGxzIiwiYmFzZURpbWVuc2lvbiIsInJlc3BvbnNpdmVNaW5SYWRpdXMiLCJyZXNwb25zaXZlUmFkaXVzUmFuZ2UiLCJpIiwicmFkaXVzIiwicmFuZG9tIiwicHVzaCIsInZ4IiwidnkiLCJ2ZXJ0ZXhTaGFkZXJTcmMiLCJmcmFnbWVudFNoYWRlclNyYyIsImNvbXBpbGVTaGFkZXIiLCJzaGFkZXJTb3VyY2UiLCJzaGFkZXJUeXBlIiwic2hhZGVyIiwiY3JlYXRlU2hhZGVyIiwiRXJyb3IiLCJnZXRTaGFkZXJQYXJhbWV0ZXIiLCJDT01QSUxFX1NUQVRVUyIsImdldFNoYWRlckluZm9Mb2ciLCJnZXRVbmlmb3JtTG9jYXRpb24iLCJwcm9ncmFtIiwibmFtZSIsInVuaWZvcm1Mb2NhdGlvbiIsImdldEF0dHJpYkxvY2F0aW9uIiwiYXR0cmlidXRlTG9jYXRpb24iLCJ2ZXJ0ZXhTaGFkZXIiLCJWRVJURVhfU0hBREVSIiwiZnJhZ21lbnRTaGFkZXIiLCJGUkFHTUVOVF9TSEFERVIiLCJjcmVhdGVQcm9ncmFtIiwiYXR0YWNoU2hhZGVyIiwibGlua1Byb2dyYW0iLCJ2ZXJ0ZXhEYXRhIiwiRmxvYXQzMkFycmF5IiwidmVydGV4RGF0YUJ1ZmZlciIsImNyZWF0ZUJ1ZmZlciIsImJpbmRCdWZmZXIiLCJBUlJBWV9CVUZGRVIiLCJidWZmZXJEYXRhIiwiU1RBVElDX0RSQVciLCJwb3NpdGlvbkhhbmRsZSIsImVuYWJsZVZlcnRleEF0dHJpYkFycmF5IiwidmVydGV4QXR0cmliUG9pbnRlciIsIkZMT0FUIiwibWV0YWJhbGxzSGFuZGxlIiwibnVtTWV0YWJhbGxzSGFuZGxlIiwid2lkdGhIYW5kbGUiLCJoZWlnaHRIYW5kbGUiLCJibHVlQ29sb3JIYW5kbGUiLCJ3aGl0ZUNvbG9ySGFuZGxlIiwiYmFja2dyb3VuZENvbG9ySGFuZGxlIiwibG9vcCIsInNjcm9sbEVmZmVjdCIsInRpbWUiLCJEYXRlIiwibWV0YWJhbGwiLCJiYXNlU3BlZWQiLCJzdGF0aWNXaWdnbGVYIiwic2luIiwic3RhdGljV2lnZ2xlWSIsImNvcyIsIm5ld1Z4IiwibmV3VnkiLCJhYnMiLCJzY3JvbGxXaWdnbGUiLCJtaW5TcGVlZCIsImRhdGFUb1NlbmRUb0dQVSIsImJhc2VJbmRleCIsIm1iIiwidW5pZm9ybTNmdiIsInVuaWZvcm0xaSIsInVuaWZvcm0xZiIsInVuaWZvcm0zZiIsImRyYXdBcnJheXMiLCJUUklBTkdMRV9TVFJJUCIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsImhhbmRsZU1vdXNlTW92ZSIsImUiLCJyZWN0IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwiY2xpZW50WCIsImxlZnQiLCJjbGllbnRZIiwidG9wIiwiaGFuZGxlUmVzaXplIiwibmV3RHByIiwidXNlUHJvZ3JhbSIsImNhbmNlbEFuaW1hdGlvbkZyYW1lIiwicmVmIiwic3R5bGUiLCJ6SW5kZXgiLCJ0cmFuc2Zvcm0iLCJuYXYiLCJzcGFuIiwiaHJlZiIsInNlY3Rpb24iLCJoMSIsImZpbHRlciIsImFuaW1hdGlvbiIsInAiLCJiYWNrZ3JvdW5kIiwiYmFja2Ryb3BGaWx0ZXIiLCJoMiIsInVsIiwibGkiLCJ2YXJpYW50IiwiaDMiLCJpZCIsInBsYWNlaG9sZGVyIiwiZW1haWxJbnB1dCIsImRvY3VtZW50IiwiZ2V0RWxlbWVudEJ5SWQiLCJlbWFpbCIsInZhbHVlIiwiZW1haWxSZWdleCIsInRlc3QiLCJidXR0b24iLCJuZXh0RWxlbWVudFNpYmxpbmciLCJ0ZXh0Q29udGVudCIsImNsYXNzTGlzdCIsInJlbW92ZSIsImFkZCIsImFsZXJ0IiwiZm9vdGVyIiwiaW1nIiwic3JjIiwiYWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});