@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-optical-sizing: auto;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  .text-display {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    letter-spacing: -0.04em;
  }

  .text-body-large {
    font-size: clamp(1rem, 3vw, 1.125rem); /* Smaller on mobile, scales up to original */
    line-height: 1.7;
    font-weight: 400;
  }
}

/* Enhanced spacing system */
.section-spacing {
  padding: clamp(2.5rem, 6vw, 8rem) 0; /* Smaller min padding for mobile */
}

.container-narrow {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.container-wide {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Custom animations */
@keyframes web3Pulse {
  0%,
  100% {
    transform: scale(1);
    filter: brightness(1.3);
  }
  50% {
    transform: scale(1.05);
    filter: brightness(1.5);
  }
}

@keyframes borderLoop {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-spin-slow {
  animation: spin 8s linear infinite;
}

.animate-fadeIn {
  animation: fadeIn 1s ease-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.15), 0 8px 32px rgba(0, 0, 0, 0.12);
}

.shadow-glow-lg {
  box-shadow: 0 0 40px rgba(59, 130, 246, 0.2), 0 16px 64px rgba(0, 0, 0, 0.15);
}

.shadow-rainbow {
  box-shadow:
    0 0 20px rgba(255, 0, 150, 0.3),
    0 0 40px rgba(0, 150, 255, 0.2),
    0 0 60px rgba(150, 255, 0, 0.1),
    0 16px 32px rgba(0, 0, 0, 0.1);
}

/* Glow effects */
.glow-blue {
  text-shadow: 0 0 20px #00a3ff, 0 0 40px #00a3ff, 0 0 60px #00a3ff;
}

.glow-purple {
  text-shadow: 0 0 20px #9d00ff, 0 0 40px #9d00ff, 0 0 60px #9d00ff;
}

/* Gradient backgrounds */
.bg-gradient-mesh {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes gradientShine {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Enhanced smooth scrolling utilities */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Momentum scrolling for touch devices */
.momentum-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
