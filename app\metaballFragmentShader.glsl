precision highp float;
uniform float u_width;
uniform float u_height;
uniform int u_numMetaballs;
uniform vec3 metaballs[21];
uniform vec3 u_blueColor;
uniform vec3 u_whiteColor;
uniform vec3 u_backgroundColor;

void main(){
  float x = gl_FragCoord.x;
  float y = gl_FragCoord.y;
  float sum = 0.0;

  for (int i = 0; i < u_numMetaballs; i++) {
    vec3 metaball = metaballs[i];
    float dx = metaball.x - x;
    float dy = metaball.y - y;
    float radius = metaball.z;
    sum += (radius * radius) / (dx * dx + dy * dy);
  }

  float threshold = 0.5;
  float edgeWidth = 0.02;

  if (sum >= threshold - edgeWidth) {
    float intensity = min(1.0, (sum - threshold) * 20.0);
    vec3 color = mix(u_blueColor, u_whiteColor, intensity);
    float alpha = smoothstep(threshold - edgeWidth, threshold + edgeWidth, sum);
    gl_FragColor = vec4(color, alpha);
    return;
  }

  gl_FragColor = vec4(u_backgroundColor, 1.0);
}
