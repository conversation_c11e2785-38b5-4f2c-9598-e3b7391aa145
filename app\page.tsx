"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CohortInfoModal } from "@/components/ui/cohort-info-modal"
import {
  ArrowRight,
  Wallet,
  Twitter,
  MessageCircle,
  FileText,
  Send,
  DollarSign,
  Rocket,
  Shield,
} from "lucide-react"
import Link from "next/link"
import { useEffect, useRef, useState } from "react"

function JoinCohortSplitButton() {
  const [selectedOption, setSelectedOption] = useState<"Project" | "Curator" | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [resetForm, setResetForm] = useState(false);

  const handleSplit = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedOption(null);
    setResetForm(true);
    resetTheForm();
  };

  const resetTheForm = () => {
    setResetForm(false);
  };

  const buttons = [
    {
      icon: Rocket,
      label: "Project",
      action: "Project",
      tooltip: "Join as a project - Get feedback, ecosystem intros, and milestone rewards",
    },
    {
      icon: Shield,
      label: "Curator",
      action: "Curator",
      tooltip: "Join as a curator - Get project intros, monetization opportunities, and rewards",
    },
  ];

  return (
    <div className="relative flex items-center justify-center h-20 mx-auto"> {/* Removed w-full */}
      {isModalOpen && (
        <CohortInfoModal
          isOpen={isModalOpen}
          onCloseAction={handleCloseModal}
        />
      )}
      <Button
        onClick={handleSplit}
        className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 text-lg md:px-12 md:py-5 md:text-xl font-bold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-opacity duration-300 max-w-xs sm:max-w-sm"
      > {/* Added responsive padding/text size and max-width */}
        Join The First Cohort
        <ArrowRight className="w-5 h-5 ml-2 md:w-6 md:h-6 md:ml-3" /> {/* Adjusted icon size */}
      </Button>
    </div>
  );
}

export default function HomePage() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const [scrollY, setScrollY] = useState(0)

  // Subtle scroll physics tracking
  const scrollPhysicsRef = useRef({ velocity: 0, lastY: 0, lastTime: 0, effect: 0 })

  // Fixed optimal animation settings
  const animationSettings = {
    numMetaballs: 21,
    edgeWidth: 0.02,
    speed: 2.1,
    threshold: 1.14,
    intensity: 12.5,
    blueColor: { r: 0.23, g: 0.51, b: 0.96 },
    whiteColor: { r: 1.0, g: 1.0, b: 1.0 },
    backgroundColor: { r: 1.0, g: 1.0, b: 1.0 }
  }

  // Handle scroll with subtle physics
  useEffect(() => {
    const handleScroll = () => {
      const currentTime = performance.now()
      const currentY = window.scrollY
      const physics = scrollPhysicsRef.current

      // Calculate velocity only if we have previous data
      if (physics.lastTime > 0) {
        const deltaTime = currentTime - physics.lastTime
        const deltaY = currentY - physics.lastY

        if (deltaTime > 0) {
          // Smooth velocity calculation
          const newVelocity = deltaY / deltaTime
          physics.velocity = physics.velocity * 0.7 + newVelocity * 0.3 // Smooth averaging

          // Create subtle effect based on velocity
          const maxEffect = 0.2 // Very subtle maximum effect
          physics.effect = Math.max(-maxEffect, Math.min(maxEffect, physics.velocity * 0.1))
        }
      }

      physics.lastTime = currentTime
      physics.lastY = currentY
      setScrollY(currentY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Gentle decay of scroll effects
  useEffect(() => {
    const decayInterval = setInterval(() => {
      const physics = scrollPhysicsRef.current
      physics.effect *= 0.88 // Very gentle decay
      physics.velocity *= 0.95
    }, 16)

    return () => clearInterval(decayInterval)
  }, [])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const dpr = window.devicePixelRatio || 1;
    const cssWidth = window.innerWidth;
    const cssHeight = window.innerHeight;

    // Use device-pixel–scaled dimensions for all WebGL maths so the effect fills the entire screen on high-DPR (e.g. mobile) displays
    const width = cssWidth * dpr;
    const height = cssHeight * dpr;

    canvas.width = width;
    canvas.height = height;

    const gl = canvas.getContext("webgl")
    if (!gl) {
      console.error("WebGL not supported")
      return
    }

    gl.viewport(0, 0, canvas.width, canvas.height); // Set viewport for DPR
    console.log("WebGL context created successfully");

    const mouse = { x: 0, y: 0 }
    const numMetaballs = animationSettings.numMetaballs
    const metaballs: Array<{
      x: number
      y: number
      vx: number
      vy: number
      r: number
    }> = []

    // Calculate responsive radii based on the smaller dimension, making them larger
    const baseDimension = Math.min(width, height);
    const responsiveMinRadius = baseDimension * 0.1; // Doubled from 0.05
    const responsiveRadiusRange = baseDimension * 0.08; // Doubled from 0.04

    // Initialize metaballs with configurable settings
    for (let i = 0; i < numMetaballs; i++) {
      const radius = Math.random() * responsiveRadiusRange + responsiveMinRadius;
      metaballs.push({
        x: Math.random() * (width - 2 * radius) + radius,
        y: Math.random() * (height - 2 * radius) + radius,
        vx: (Math.random() - 0.5) * animationSettings.speed,
        vy: (Math.random() - 0.5) * animationSettings.speed,
        r: radius * 0.75,
      })
    }

    const vertexShaderSrc = `
      attribute vec2 position;
      void main() {
        gl_Position = vec4(position, 0.0, 1.0);
      }
    `

    // Helper functions for WebGL
    const compileShader = (gl: WebGLRenderingContext, shaderSource: string, shaderType: number) => {
      const shader = gl.createShader(shaderType)
      if (!shader) throw new Error("Could not create shader")

      gl.shaderSource(shader, shaderSource)
      gl.compileShader(shader)

      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        throw new Error("Shader compile failed with: " + gl.getShaderInfoLog(shader))
      }
      return shader
    }

    const getUniformLocation = (gl: WebGLRenderingContext, program: WebGLProgram, name: string) => {
      const uniformLocation = gl.getUniformLocation(program, name)
      if (uniformLocation === -1) {
        throw new Error("Can not find uniform " + name)
      }
      return uniformLocation
    }

    const getAttribLocation = (gl: WebGLRenderingContext, program: WebGLProgram, name: string) => {
      const attributeLocation = gl.getAttribLocation(program, name)
      if (attributeLocation === -1) {
        throw new Error("Can not find attribute " + name)
      }
      return attributeLocation
    }

    const vertexShader = compileShader(gl, vertexShaderSrc, gl.VERTEX_SHADER)
    const fragmentShader = compileShader(gl, fragmentShaderSrc, gl.FRAGMENT_SHADER)
    const program = gl.createProgram()
    if (!program) throw new Error("Could not create program")

    gl.attachShader(program, vertexShader)
    gl.attachShader(program, fragmentShader)
    gl.linkProgram(program)

    const vertexData = new Float32Array([
      -1.0, 1.0, // top left
      -1.0, -1.0, // bottom left
      1.0, 1.0, // top right
      1.0, -1.0, // bottom right
    ])

    const vertexDataBuffer = gl.createBuffer()
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexDataBuffer)
    gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW)

    const positionHandle = getAttribLocation(gl, program, "position")
    gl.enableVertexAttribArray(positionHandle)
    gl.vertexAttribPointer(positionHandle, 2, gl.FLOAT, false, 2 * 4, 0)

    const metaballsHandle = getUniformLocation(gl, program, "metaballs")
    const numMetaballsHandle = getUniformLocation(gl, program, "u_numMetaballs");
    const blueColorHandle = getUniformLocation(gl, program, "u_blueColor");
    const whiteColorHandle = getUniformLocation(gl, program, "u_whiteColor");
    const backgroundColorHandle = getUniformLocation(gl, program, "u_backgroundColor");

    const loop = () => {
      // Get subtle scroll physics effect
      const scrollEffect = scrollPhysicsRef.current.effect
      const time = Date.now() * 0.001 // Convert to seconds

      // Update metaballs with continuous movement and scroll-responsive effects
      for (let i = 0; i < numMetaballs; i++) {
        const metaball = metaballs[i]

        // Base continuous movement - always active (slowed down)
        const baseSpeed = animationSettings.speed * 0.077 // 15% of original speed as base (half of previous)
        const staticWiggleX = Math.sin(time * 0.25 + i * 1.2) * baseSpeed * 0.4 // Half frequency
        const staticWiggleY = Math.cos(time * 0.15 + i * 0.8) * baseSpeed * 0.3 // Half frequency

        // Apply base movement with static wiggle (reduced intensity)
        let newVx = metaball.vx + staticWiggleX * 0.06
        let newVy = metaball.vy + staticWiggleY * 0.05

        // Add scroll physics on top of base movement
        if (Math.abs(scrollEffect) > 0.01) {
          // Subtle vertical drift based on scroll direction
          newVy += scrollEffect * 0.25

          // Enhanced horizontal wiggle during scroll (slowed down)
          const scrollWiggle = Math.sin(time * 1 + i * 0.7) * Math.abs(scrollEffect) * 0.1 // Half frequency
          newVx += scrollWiggle

          // Tiny bit of randomness for natural variation
          newVx += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.05
          newVy += (Math.random() - 0.5) * Math.abs(scrollEffect) * 0.03
        }

        // Ensure minimum movement to prevent stagnation
        const minSpeed = 0.2
        if (Math.abs(newVx) < minSpeed) newVx += (Math.random() - 0.5) * minSpeed
        if (Math.abs(newVy) < minSpeed) newVy += (Math.random() - 0.5) * minSpeed

        // Update position with modified velocities
        metaball.x += newVx
        metaball.y += newVy

        // Store back the velocities with less aggressive damping
        metaball.vx = newVx * 0.995 // Less damping to maintain movement
        metaball.vy = newVy * 0.995

        // Boundary collision
        if (metaball.x < metaball.r || metaball.x > width - metaball.r) metaball.vx *= -1
        if (metaball.y < metaball.r || metaball.y > height - metaball.r) metaball.vy *= -1
      }

      const dataToSendToGPU = new Float32Array(3 * numMetaballs)
      for (let i = 0; i < numMetaballs; i++) {
        const baseIndex = 3 * i
        const mb = metaballs[i]
        dataToSendToGPU[baseIndex + 0] = mb.x
        dataToSendToGPU[baseIndex + 1] = mb.y
        dataToSendToGPU[baseIndex + 2] = mb.r
      }

      gl.uniform3fv(metaballsHandle, dataToSendToGPU)
      gl.uniform1i(numMetaballsHandle, numMetaballs); // Set numMetaballs uniform
      gl.uniform3f(blueColorHandle, animationSettings.blueColor.r, animationSettings.blueColor.g, animationSettings.blueColor.b);
      gl.uniform3f(whiteColorHandle, animationSettings.whiteColor.r, animationSettings.whiteColor.g, animationSettings.whiteColor.b);
      gl.uniform3f(backgroundColorHandle, animationSettings.backgroundColor.r, animationSettings.backgroundColor.g, animationSettings.backgroundColor.b);
      gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4)

      animationRef.current = requestAnimationFrame(loop)
    }

    // Mouse interaction
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect()
      mouse.x = e.clientX - rect.left
      mouse.y = e.clientY - rect.top
    }

    canvas.addEventListener("mousemove", handleMouseMove)

    // Handle resize
    const handleResize = () => {
      const newDpr = window.devicePixelRatio || 1;
      canvas.width = window.innerWidth * newDpr;
      canvas.height = window.innerHeight * newDpr;
      gl.viewport(0, 0, canvas.width, canvas.height);
    }

    window.addEventListener("resize", handleResize)

    gl.useProgram(program);
    loop();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      canvas.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("resize", handleResize);
    };
  }, [])

  return (
    <div className="min-h-screen bg-white relative overflow-x-hidden">
      {/* WebGL Canvas Background */}
      <canvas
        ref={canvasRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{
          zIndex: 1,
          transform: `translateY(${scrollY * 0.3}px)` // Parallax effect
        }}
      />



      {/* Navigation */}
      <nav className={`bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300 ${
        scrollY > 50 ? 'shadow-lg' : 'shadow-sm'
      }`}>
        <div className="container-wide">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">F</span>
              </div>
              <span className="text-gray-900 font-bold text-xl tracking-tight">Forcefi</span>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <Link href="#" className="text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group">
                Projects
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full"></span>
              </Link>
              <Link href="#" className="text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group">
                Curators
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full"></span>
              </Link>
              <Link href="#" className="text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group">
                Community
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full"></span>
              </Link>
              <Link href="#" className="text-gray-700 hover:text-blue-600 transition-all duration-200 text-sm font-medium relative group">
                Docs
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full"></span>
              </Link>

              <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                <Wallet className="w-4 h-4 mr-2" />
                Connect Wallet
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Banner */}
      <section className="relative py-20 lg:py-32 min-h-screen flex items-center justify-center">
        <div className="max-w-6xl mx-auto px-6 text-center relative z-10">
          
          <h1 className="text-display text-gray-900 mb-8 max-w-4xl mx-auto">
            Discover & Support{" "}
            <span
              className="bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 bg-clip-text text-transparent"
              style={{
                filter: "drop-shadow(0 0 20px rgba(59, 130, 246, 0.3))",
                animation: "web3Pulse 3s ease-in-out infinite",
              }}
            >
              Web3 Startups
            </span>
          </h1>
          

          <p className="text-body-large text-gray-600 mb-12 max-w-4xl mx-auto">
            Forcefi is a decentralized launchpad that bridges industry experts with Web3 Startups. 
            List your early-stage project, or monetize your skills as a platform curator.
            
          </p>

          <div className="flex flex-col items-center gap-10">
            <JoinCohortSplitButton />

            {/* Enhanced Stats */}
            {/* Removed the 200+ Expert Curators and 70+ Listed Projects cards as requested */}
          </div>

          {/* Social Links */}
          <div className="flex flex-wrap justify-center items-center gap-4 mt-8 sm:gap-6"> {/* Adjusted gap for mobile */}
            <Link href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium"> {/* Adjusted space-x and text size */}
              <Twitter className="w-4 h-4 sm:w-5 sm:h-5" /> {/* Adjusted icon size */}
              <span>Follow on X</span>
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium">
              <MessageCircle className="w-4 h-4 sm:w-5 sm:h-5" />
              <span>Join Discord</span>
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium">
              <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
              <span>Read Documentation</span>
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center space-x-1.5 text-xs sm:text-sm font-medium">
              <Send className="w-4 h-4 sm:w-5 sm:h-5" />
              <span>Join Telegram</span>
            </Link>
          </div>
        </div>
      </section>

      {/* First Cohort Information */}
      <div className="py-16 relative z-20" style={{
        background: "linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5) 70%, rgba(255, 255, 255, 0) 100%)",
        backdropFilter: "blur(7px)"
      }}>
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-extrabold text-gray-900 mb-6">First Cohort Benefits</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
              As part of Forcefi’s first cohort, you receive a number of benefits that help shape your product and build an audience for your project and services. Here is a breakdown of what you can expect:
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Projects */}
            <Card className="bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-left">
              <CardHeader className="pb-4 pt-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow">
                  <Rocket className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl font-semibold text-gray-900 mb-2 text-center">Projects</CardTitle>
              </CardHeader>
              <CardContent className="pb-6 px-8">
                <ul className="text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left">
                  <li>Biweekly feedback sessions on product, marketing, and BD</li>
                  <li>Contextually relevant product deepdive & advice</li>
                  <li>Ecosystem intros and relevant partnership opportunities</li>
                  <li>Educational materials and discounts on external courses</li>
                  <li>Milestone-dependent support in token grants</li>
                </ul>
              </CardContent>
            </Card>

            {/* Curators */}
            <Card className="bg-blue-50 border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 text-center">
              <CardHeader className="pb-4 pt-8">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl font-semibold text-gray-900 mb-2 text-center">Curators</CardTitle>
              </CardHeader>
              <CardContent className="pb-6 px-8">
                <ul className="text-base text-gray-700 leading-relaxed list-disc list-inside space-y-2 text-left">
                  <li>Intros to projects that require your services</li>
                  <li>Monetization opportunities through platform participation</li>
                  <li>Small pool of competitors (only the best are accepted)</li>
                  <li>Increase your reach and grow your audience</li>
                  <li>Milestone-dependent token rewards</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Audience Segments */}
        <div className="section-spacing relative z-20">
          <div className="container-wide">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Three Ways to Participate</h2>
              <p className="text-body-large text-gray-600 max-w-2xl mx-auto">
                Join our ecosystem as a project, curator, or investor and shape the Web3 future
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Projects */}
              <Card className="bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center">
                <CardHeader className="pb-6 pt-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <Rocket className="w-10 h-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-4">Projects</CardTitle>
                </CardHeader>
                <CardContent className="pb-10">
                  <CardDescription className="text-gray-600 text-lg leading-relaxed mb-8">
Access our vetted network of 70+ Listed Projects to find talent and growth opportunities. Accelerate your project with community expertise.
                  </CardDescription>
                  <Button variant="ghost" className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-semibold text-base px-6 py-3 rounded-lg">
                    Learn More →
                  </Button>
                </CardContent>
              </Card>

              {/* Curators */}
              <Card className="bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center">
                <CardHeader className="pb-6 pt-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <Shield className="w-10 h-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-4">Curators</CardTitle>
                </CardHeader>
                <CardContent className="pb-10">
                  <CardDescription className="text-gray-600 text-lg leading-relaxed mb-8">
Support promising projects and earn income. Join our network of 200+ Expert Curators making a real impact in Web3.
                  </CardDescription>
                  <Button variant="ghost" className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 font-semibold text-base px-6 py-3 rounded-lg">
                    Get Involved →
                  </Button>
                </CardContent>
              </Card>

              {/* Investors */}
              <Card className="bg-white border-0 shadow-md hover:shadow-lg transition-shadow duration-300 text-center">
                <CardHeader className="pb-6 pt-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <DollarSign className="w-10 h-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-4">Investors</CardTitle>
                </CardHeader>
                <CardContent className="pb-10">
                  <CardDescription className="text-gray-600 text-lg leading-relaxed mb-8">
Invest in curated projects at the earliest stage. Earn through staking and support innovative Web3 projects. Access exclusive early-stage opportunities backed by our expert curators.
                  </CardDescription>
                  <Button variant="ghost" className="text-green-600 hover:text-green-700 hover:bg-green-50 font-semibold text-base px-6 py-3 rounded-lg">
                    Explore Now →
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Token Launch Teaser & Waitlist Combined */}
        <div className="py-20 relative z-20">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="max-w-4xl mx-auto px-6 text-center relative z-10 mb-24">
            <Card className="shadow-2xl hover:shadow-3xl transition-all duration-300">
              <CardHeader className="pt-12 pb-6">
                <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <Rocket className="w-8 h-8 text-gray-600" />
                </div>
                <CardTitle className="text-3xl font-bold text-gray-900 mb-4">Something Big is Coming</CardTitle>
              </CardHeader>
              <CardContent className="pb-12">
                <CardDescription className="text-gray-600 text-lg leading-relaxed mb-8">
                  Stay tuned for Forcefi's token launch, empowering our ecosystem. Details soon!
                </CardDescription>
              </CardContent>
            </Card>
          </div>
          <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
            {/* Waitlist Part */}
            <div className="flex flex-col items-center mb-12">
              <Shield className="w-10 h-10 text-gray-600" />
              <h3 className="text-2xl font-bold text-gray-900 mt-4">Join the Waitlist</h3>
              <p className="text-lg text-gray-600 mt-2">Be the first to know when we launch</p>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Be the First to Experience Forcefi</h2>
            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Follow our socials and learn more about our platform to benefit as an early-stage user. If you have questions feel free to reach out and our team will get back to you.
            </p>

            {/* Waitlist Signup */}
            <div className="max-w-md mx-auto mb-12 px-4 sm:px-0"> {/* Added horizontal padding for small screens */}
              <div className="flex flex-col sm:flex-row gap-3"> {/* Changed to flex-col on mobile, flex-row on sm+ */}
                <Input
                  id="emailInput"
                  placeholder="Enter your email"
                  className="bg-white border-gray-300 focus:border-blue-500 text-gray-900 placeholder:text-gray-500 text-base shadow-sm flex-grow"
                />
                <Button
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 whitespace-nowrap font-semibold shadow-lg w-full sm:w-auto" // Made button full width on mobile
                  onClick={() => {
                    const emailInput = document.getElementById('emailInput') as HTMLInputElement;
                    const email = emailInput.value;
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                    if (emailRegex.test(email)) {
                      const button = emailInput.nextElementSibling as HTMLButtonElement;
                      button.textContent = 'Subscribed';
                      button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                      button.classList.add('bg-green-600', 'hover:bg-green-700');
                    } else {
                      alert('Please enter a valid email address.');
                    }
                  }}
                >
                  Join the Waitlist
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-gray-900 py-16 relative z-20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8">
              {/* Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center p-1">
                  <img
                    src="/logo.svg"
                    alt="Forcefi Logo"
                    className="w-full h-full object-contain"
                    style={{ filter: 'brightness(0) invert(1)' }}
                  />
                </div>
                <span className="text-white font-bold text-2xl">Forcefi</span>
              </div>

              {/* Social Links - Horizontal Row */}
              <div className="flex items-center space-x-6">
                <Link href="#" className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2">
                  <Twitter className="w-5 h-5" />
                  <span className="text-sm">Follow on X</span>
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2">
                  <MessageCircle className="w-5 h-5" />
                  <span className="text-sm">Join Discord</span>
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span className="text-sm">Read Documentation</span>
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center space-x-2">
                  <Send className="w-5 h-5" />
                  <span className="text-sm">Join Telegram</span>
                </Link>
              </div>

              {/* Copyright */}
              <div className="text-gray-400 text-sm">© 2025 Forcefi. All rights reserved.</div>
            </div>
          </div>
        </footer>
      </div>

      <style jsx>{`
        @keyframes web3Pulse {
          0%, 100% {
            transform: scale(1);
            filter: brightness(1.1);
          }
          50% {
            transform: scale(1.02);
            filter: brightness(1.3);
          }
        }

        .holographic-card {
          width: 100%;
          background: #111;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          overflow: hidden;
          border-radius: 15px;
          transition: all 0.5s ease;
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .holographic-card:hover {
          transform: scale(1.05);
          box-shadow: 0 0 20px rgba(0,255,255,0.5), 0 10px 20px rgba(0, 0, 0, 0.3);
        }
      `}</style>
    </div>
  )
}
