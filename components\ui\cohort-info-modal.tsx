"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Cross2Icon } from "@radix-ui/react-icons"
import { FormField, ProjectFormData, CuratorFormData } from "./form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Users, UserCheck, ArrowLeft, ArrowRight, CheckCircle, X } from "lucide-react"

interface CohortInfoModalProps {
  isOpen: boolean;
  onCloseAction: () => void; // Renamed from onClose
}

const expertiseOptions = [
  "Blockchain Development",
  "Smart Contracts",
  "DeFi Protocols",
  "NFT Development",
  "Web3 Marketing",
  "Tokenomics",
  "Community Building",
  "Product Strategy",
  "Fundraising",
  "Legal & Compliance",
  "UI/UX Design",
  "Security Auditing",
]

export function CohortInfoModal({ isOpen, onCloseAction }: CohortInfoModalProps) {
  const [selectedRole, setSelectedRole] = useState<"project" | "curator" | null>(null)
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitted, setIsSubmitted] = useState(false);

  const [projectFormData, setProjectFormData] = useState<ProjectFormData>({
    title: "",
    website: "",
    chain: "",
    type: "",
    description: "",
    teamMembers: "",
    email: "",
    goals: "",
    twitter: "",
    discord: "",
    telegram: "",
  })
  const [otherChain, setOtherChain] = useState("");

  const [curatorFormData, setCuratorFormData] = useState<CuratorFormData>({
    name: "",
    services: "",
    documents: "",
    availability: "",
    expertise: [],
    email: "",
    twitter: "",
    linkedin: "",
    discord: "",
  })

  const resetForm = () => {
    setSelectedRole(null)
    setCurrentStep(1)
    setProjectFormData({
      title: "",
      website: "",
      chain: "",
      type: "",
      description: "",
      teamMembers: "",
      email: "",
      goals: "",
      twitter: "",
      discord: "",
      telegram: "",
    })
    setCuratorFormData({
      name: "",
      services: "",
      documents: "",
      availability: "",
      expertise: [],
      email: "",
      twitter: "",
      linkedin: "",
      discord: "",
    })
  }

  const handleClose = () => {
    resetForm();
    onCloseAction(); // Updated reference
  };

  const handleRoleSelect = (role: "project" | "curator") => {
    setSelectedRole(role)
    setCurrentStep(2)
  }

  const handleBack = () => {
    if (currentStep === 2) {
      setSelectedRole(null)
      setCurrentStep(1)
    } else {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleNext = () => {
    setCurrentStep(currentStep + 1)
  }

  const handleSubmit = () => {
    const formData = selectedRole === "project" ? projectFormData : curatorFormData;
    console.log(`${selectedRole} application submitted:`, formData);
    // Here you would typically send the data to your backend
    setIsSubmitted(true);
  }

  const toggleExpertise = (skill: string) => {
    setCuratorFormData((prev) => ({
      ...prev,
      expertise: prev.expertise.includes(skill)
        ? prev.expertise.filter((s) => s !== skill)
        : [...prev.expertise, skill],
    }))
  }

  const getProgressPercentage = () => {
    if (!selectedRole) return 0
    return ((currentStep - 1) / 3) * 100
  }

  const renderRoleSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <p className="text-gray-600">Choose your path to shape the future of Web3</p>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        <Card
          className="cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-blue-500"
          onClick={() => handleRoleSelect("project")}
        >
          <CardHeader className="text-center pb-4">
            <div className="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-blue-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">Project</CardTitle>
          </CardHeader>
          <CardContent className="text-center pb-8">
            <CardDescription className="text-gray-600">
              Apply with your Web3 project to get expert guidance and community support
            </CardDescription>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-md transition-all duration-300 border-2 hover:border-green-500"
          onClick={() => handleRoleSelect("curator")}
        >
          <CardHeader className="text-center pb-4">
            <div className="w-16 h-16 bg-green-50 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <UserCheck className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">Curator</CardTitle>
          </CardHeader>
          <CardContent className="text-center pb-8">
            <CardDescription className="text-gray-600">
              Share your expertise and earn by mentoring the next generation of Web3 startups
            </CardDescription>
          </CardContent>
        </Card>
      </div>
    </div>
  )

  const renderProjectForm = () => {
    switch (currentStep) {
      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project Title *</label>
                <Input
                  placeholder="Enter your project name"
                  value={projectFormData.title}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, title: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Website URL</label>
                <Input
                  placeholder="https://yourproject.com"
                  value={projectFormData.website}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, website: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Blockchain *</label>
                <select
                  value={projectFormData.chain}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, chain: e.target.value }))}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Select blockchain</option>
                  <option value="ethereum">Ethereum</option>
                  <option value="polygon">Polygon</option>
                  <option value="bsc">BSC</option>
                  <option value="solana">Solana</option>
                  <option value="other">Other</option>
                </select>
                {projectFormData.chain === "other" && (
                  <Input
                    placeholder="Please specify"
                    value={otherChain}
                    onChange={(e) => {
                      setOtherChain(e.target.value);
                      setProjectFormData(prev => ({...prev, type: e.target.value}))
                    }}
                    className="mt-2"
                  />
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project Type *</label>
                <select
                  value={projectFormData.type}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, type: e.target.value }))}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Select project type</option>
                  <option value="defi">DeFi</option>
                  <option value="nft">NFT</option>
                  <option value="gaming">Gaming</option>
                  <option value="infrastructure">Infrastructure</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
          </div>
        )
      case 3:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Team & Contact</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project Description *</label>
                <Textarea
                  placeholder="Describe your project, its goals, and what makes it unique..."
                  value={projectFormData.description}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, description: e.target.value }))}
                  rows={4}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Team Members</label>
                <Input
                  placeholder="List key team members and their roles"
                  value={projectFormData.teamMembers}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, teamMembers: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Contact Email *</label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={projectFormData.email}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, email: e.target.value }))}
                />
              </div>
            </div>
          </div>
        )
      case 4:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Goals & Social</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project Goals *</label>
                <Textarea
                  placeholder="What do you hope to achieve through this cohort?"
                  value={projectFormData.goals}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, goals: e.target.value }))}
                  rows={3}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Twitter</label>
                <Input
                  placeholder="@yourproject"
                  value={projectFormData.twitter}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, twitter: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Discord</label>
                <Input
                  placeholder="Discord server invite"
                  value={projectFormData.discord}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, discord: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Telegram</label>
                <Input
                  placeholder="Telegram group/channel"
                  value={projectFormData.telegram}
                  onChange={(e) => setProjectFormData((prev) => ({ ...prev, telegram: e.target.value }))}
                />
              </div>
            </div>
          </div>
        )
      default:
        return null
    }
  }

  const renderCuratorForm = () => {
    switch (currentStep) {
      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                <Input
                  placeholder="Enter your full name"
                  value={curatorFormData.name}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Services Offered *</label>
                <Textarea
                  placeholder="Describe the services and expertise you can provide to Web3 projects..."
                  value={curatorFormData.services}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, services: e.target.value }))}
                  rows={4}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Portfolio/Documents</label>
                <Input
                  placeholder="Links to your portfolio, resume, or relevant documents"
                  value={curatorFormData.documents}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, documents: e.target.value }))}
                />
              </div>
            </div>
          </div>
        )
      case 3:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Availability & Expertise</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Availability *</label>
                <select
                  value={curatorFormData.availability}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, availability: e.target.value }))}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Select your availability</option>
                  <option value="full-time">Full-time</option>
                  <option value="part-time">Part-time</option>
                  <option value="weekends">Weekends</option>
                  <option value="flexible">Flexible</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Expertise Areas *</label>
                <div className="flex flex-wrap gap-2">
                  {expertiseOptions.map((skill) => (
                    <Badge
                      key={skill}
                      variant={curatorFormData.expertise.includes(skill) ? "default" : "outline"}
                      className={`cursor-pointer transition-colors ${
                        curatorFormData.expertise.includes(skill) ? "bg-blue-600 hover:bg-blue-700" : "hover:bg-blue-50"
                      }`}
                      onClick={() => toggleExpertise(skill)}
                    >
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )
      case 4:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={curatorFormData.email}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, email: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Twitter</label>
                <Input
                  placeholder="@yourusername"
                  value={curatorFormData.twitter}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, twitter: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">LinkedIn</label>
                <Input
                  placeholder="LinkedIn profile URL"
                  value={curatorFormData.linkedin}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, linkedin: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Discord</label>
                <Input
                  placeholder="Discord username"
                  value={curatorFormData.discord}
                  onChange={(e) => setCuratorFormData((prev) => ({ ...prev, discord: e.target.value }))}
                />
              </div>
            </div>
          </div>
        )
      default:
        return null
    }
  }

  const renderThankYouMessage = () => (
    <div className="text-center space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">Thank you for applying to Forcefi’s first cohort.</h3>
      <p className="text-gray-600">We will review your submissions and be in touch.</p>
    </div>
  )

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="relative">
          {!isSubmitted && (
            <DialogTitle className="text-center">
              Join The First Cohort
            </DialogTitle>
          )}
        </DialogHeader>

        {isSubmitted ? (
          renderThankYouMessage()
        ) : (
          <>
            {selectedRole && (
              <div className="mb-6">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                  <span>Step {currentStep - 1} of 3</span>
                  <span>{Math.round(getProgressPercentage())}% Complete</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getProgressPercentage()}%` }}
                  />
                </div>
              </div>
            )}

            <div className="space-y-6">
              {!selectedRole && renderRoleSelection()}
              {selectedRole === "project" && renderProjectForm()}
              {selectedRole === "curator" && renderCuratorForm()}
            </div>

            {selectedRole && (
              <div className="flex justify-between pt-6 border-t">
                <Button variant="outline" onClick={handleBack} className="flex items-center gap-2 bg-transparent">
                  <ArrowLeft className="w-4 h-4" />
                  Back
                </Button>

                {currentStep < 4 ? (
                  <Button onClick={handleNext} className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700">
                    Next
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                ) : (
                  <Button onClick={handleSubmit} className="flex items-center gap-2 bg-green-600 hover:bg-green-700">
                    <CheckCircle className="w-4 h-4" />
                    Submit Application
                  </Button>
                )}
              </div>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}

// Add CSS for the hover effect
const JoinWaitlistButton = () => (
  <a
    href="#"
    className="rainbow-button"
    alt="Join the Waitlist"
    style={{
      width: "calc(20vw + 6px)",
      height: "calc(8vw + 6px)",
      backgroundImage: "linear-gradient(90deg, #00C0FF 0%, #FFCF00 49%, #FC4F4F 80%, #00C0FF 100%)",
      borderRadius: "5px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      textTransform: "uppercase",
      fontSize: "3vw",
      fontWeight: "bold",
      animation: "slidebg 2s linear infinite",
    }}
  >
    Join the Waitlist
  </a>
);
