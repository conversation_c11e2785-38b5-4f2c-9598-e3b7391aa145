"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export interface FormField {
  label: string
  type: "text" | "textarea" | "select" | "file" | "multiselect"
  placeholder?: string
  options?: string[]
  required?: boolean
}

export interface ProjectFormData {
  title: string;
  website: string;
  chain: string;
  type: string;
  description: string;
  teamMembers: string;
  email: string;
  goals: string;
  twitter: string;
  discord: string;
  telegram: string;
}

export interface CuratorFormData {
  name: string;
  services: string;
  documents: string;
  availability: string;
  expertise: string[];
  email: string;
  twitter: string;
  linkedin: string;
  discord: string;
}
